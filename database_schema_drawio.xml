<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="draw.io" etag="example" version="22.1.16">
  <diagram name="錄音室租借管理系統ER圖" id="example">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- users 表 -->
        <mxCell id="users" value="users&#xa;&#xa;id (PK)&#xa;name&#xa;email&#xa;password&#xa;email_verified_at&#xa;remember_token&#xa;created_at&#xa;updated_at" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="160" height="180" as="geometry" />
        </mxCell>
        
        <!-- user_profiles 表 -->
        <mxCell id="user_profiles" value="user_profiles&#xa;&#xa;id (PK)&#xa;user_id (FK)&#xa;phone&#xa;address&#xa;member_level&#xa;points&#xa;created_at&#xa;updated_at" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="280" y="40" width="160" height="180" as="geometry" />
        </mxCell>
        
        <!-- studios 表 -->
        <mxCell id="studios" value="studios&#xa;&#xa;id (PK)&#xa;name&#xa;description&#xa;capacity&#xa;size&#xa;location&#xa;status&#xa;created_at&#xa;updated_at" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="520" y="40" width="160" height="200" as="geometry" />
        </mxCell>
        
        <!-- bookings 表 -->
        <mxCell id="bookings" value="bookings&#xa;&#xa;id (PK)&#xa;user_id (FK)&#xa;studio_id (FK)&#xa;start_time&#xa;end_time&#xa;total_hours&#xa;total_amount&#xa;status&#xa;notes&#xa;created_at&#xa;updated_at" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="280" y="300" width="160" height="240" as="geometry" />
        </mxCell>
        
        <!-- payments 表 -->
        <mxCell id="payments" value="payments&#xa;&#xa;id (PK)&#xa;booking_id (FK)&#xa;amount&#xa;payment_method&#xa;transaction_id&#xa;status&#xa;payment_details&#xa;created_at&#xa;updated_at" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="600" width="160" height="200" as="geometry" />
        </mxCell>
        
        <!-- access_codes 表 -->
        <mxCell id="access_codes" value="access_codes&#xa;&#xa;id (PK)&#xa;booking_id (FK)&#xa;qr_code&#xa;valid_from&#xa;valid_until&#xa;is_used&#xa;used_at&#xa;created_at&#xa;updated_at" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="520" y="600" width="160" height="200" as="geometry" />
        </mxCell>
        
        <!-- 關聯線 users -> user_profiles (1:1) -->
        <mxCell id="rel_users_profiles" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=ERone;startFill=0;endArrow=ERone;endFill=0;" edge="1" parent="1" source="users" target="user_profiles">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 關聯線 users -> bookings (1:N) -->
        <mxCell id="rel_users_bookings" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=ERone;startFill=0;endArrow=ERmany;endFill=0;" edge="1" parent="1" source="users" target="bookings">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 關聯線 studios -> bookings (1:N) -->
        <mxCell id="rel_studios_bookings" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=ERone;startFill=0;endArrow=ERmany;endFill=0;" edge="1" parent="1" source="studios" target="bookings">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 關聯線 bookings -> payments (1:1) -->
        <mxCell id="rel_bookings_payments" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=ERone;startFill=0;endArrow=ERone;endFill=0;" edge="1" parent="1" source="bookings" target="payments">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 關聯線 bookings -> access_codes (1:1) -->
        <mxCell id="rel_bookings_access" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=ERone;startFill=0;endArrow=ERone;endFill=0;" edge="1" parent="1" source="bookings" target="access_codes">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
