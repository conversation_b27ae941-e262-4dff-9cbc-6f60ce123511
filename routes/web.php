<?php

use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;

Route::get('/', function () {
    return view('welcome');
})->name('home');

Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

// 公開頁面 (不需要登入)
Route::view('studios/public', 'public.studios')
    ->name('studios.public');
Route::view('pricing/public', 'public.pricing')
    ->name('pricing.public');
Route::view('about/public', 'public.about')
    ->name('about.public');
Route::view('contact/public', 'public.contact')
    ->name('contact.public');

// 會員專用路由
Route::middleware(['auth'])->group(function () {
    Route::view('studios', 'dashboard')->name('studios.index');
    Route::view('pricing', 'dashboard')->name('pricing');
    Route::view('favorites', 'dashboard')->name('favorites');
    Route::view('bookings', 'dashboard')->name('bookings.index');
    Route::view('bookings/history', 'dashboard')->name('bookings.history');
    Route::view('reviews', 'dashboard')->name('reviews.index');
    Route::view('profile/edit', 'dashboard')->name('profile.edit');
});

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Volt::route('settings/profile', 'settings.profile')->name('settings.profile');
    Volt::route('settings/password', 'settings.password')->name('settings.password');
    Volt::route('settings/appearance', 'settings.appearance')->name('settings.appearance');
});

require __DIR__.'/auth.php';
