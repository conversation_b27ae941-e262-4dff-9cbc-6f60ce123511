<?php

use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;

Route::get('/', function () {
    return view('welcome');
})->name('home');

Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

// 公開頁面 (不需要登入)
Route::view('studios/public', 'public.studios')
    ->name('studios.public');
Route::view('pricing/public', 'public.pricing')
    ->name('pricing.public');
Route::view('about/public', 'public.about')
    ->name('about.public');
Route::view('contact/public', 'public.contact')
    ->name('contact.public');

// 錄音室相關路由
Route::middleware(['auth'])->group(function () {
    Route::get('studios', function () {
        return view('test-header'); // 暫時使用測試頁面
    })->name('studios.index');

    Route::get('pricing', function () {
        return view('test-header'); // 暫時使用測試頁面
    })->name('pricing');

    Route::get('favorites', function () {
        return view('test-header'); // 暫時使用測試頁面
    })->name('favorites');

    Route::get('about', function () {
        return view('test-header'); // 暫時使用測試頁面
    })->name('about');

    Route::get('contact', function () {
        return view('test-header'); // 暫時使用測試頁面
    })->name('contact');

    Route::get('bookings', function () {
        return view('test-header'); // 暫時使用測試頁面
    })->name('bookings.index');

    Route::get('bookings/history', function () {
        return view('test-header'); // 暫時使用測試頁面
    })->name('bookings.history');

    Route::get('reviews', function () {
        return view('test-header'); // 暫時使用測試頁面
    })->name('reviews.index');

    Route::get('profile/edit', function () {
        return view('test-header'); // 暫時使用測試頁面
    })->name('profile.edit');
});

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Volt::route('settings/profile', 'settings.profile')->name('settings.profile');
    Volt::route('settings/password', 'settings.password')->name('settings.password');
    Volt::route('settings/appearance', 'settings.appearance')->name('settings.appearance');
});

require __DIR__.'/auth.php';
