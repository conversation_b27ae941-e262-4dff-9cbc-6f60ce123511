# 錄音室租借管理系統 - 資料庫關聯圖

## 資料表關聯圖

```mermaid
erDiagram
    users {
        bigint id PK
        string name
        string email
        string password
        timestamp email_verified_at
        string remember_token
        timestamp created_at
        timestamp updated_at
    }
    
    user_profiles {
        bigint id PK
        bigint user_id FK
        string phone
        text address
        enum member_level
        int points
        timestamp created_at
        timestamp updated_at
    }
    
    studios {
        bigint id PK
        string name
        text description
        int capacity
        decimal size
        string location
        enum status
        timestamp created_at
        timestamp updated_at
    }
    
    studio_images {
        bigint id PK
        bigint studio_id FK
        string image_path
        boolean is_cover
        timestamp created_at
        timestamp updated_at
    }
    
    equipments {
        bigint id PK
        string name
        text description
        enum type
        timestamp created_at
        timestamp updated_at
    }
    
    studio_equipments {
        bigint id PK
        bigint studio_id FK
        bigint equipment_id FK
        int quantity
        timestamp created_at
        timestamp updated_at
    }
    
    pricing_plans {
        bigint id PK
        bigint studio_id FK
        string name
        enum type
        decimal hourly_rate
        int min_hours
        decimal discount_percentage
        int discount_hours
        timestamp created_at
        timestamp updated_at
    }
    
    bookings {
        bigint id PK
        bigint user_id FK
        bigint studio_id FK
        datetime start_time
        datetime end_time
        decimal total_hours
        decimal total_amount
        enum status
        text notes
        timestamp created_at
        timestamp updated_at
    }
    
    payments {
        bigint id PK
        bigint booking_id FK
        decimal amount
        enum payment_method
        string transaction_id
        enum status
        json payment_details
        timestamp created_at
        timestamp updated_at
    }
    
    access_codes {
        bigint id PK
        bigint booking_id FK
        text qr_code
        datetime valid_from
        datetime valid_until
        boolean is_used
        datetime used_at
        timestamp created_at
        timestamp updated_at
    }
    
    notifications {
        bigint id PK
        bigint user_id FK
        bigint booking_id FK
        enum type
        string title
        text content
        boolean is_read
        datetime sent_at
        timestamp created_at
        timestamp updated_at
    }
    
    booking_rules {
        bigint id PK
        int min_booking_hours
        int buffer_time_minutes
        int advance_booking_days
        text cancellation_policy
        int max_booking_hours_per_day
        int max_concurrent_bookings
        boolean allow_weekend_booking
        time booking_start_time
        time booking_end_time
        timestamp created_at
        timestamp updated_at
    }
    
    %% 關聯關係
    users ||--|| user_profiles : "一對一"
    users ||--o{ bookings : "一對多"
    users ||--o{ notifications : "一對多"
    
    studios ||--o{ studio_images : "一對多"
    studios ||--o{ studio_equipments : "一對多"
    studios ||--o{ pricing_plans : "一對多"
    studios ||--o{ bookings : "一對多"
    
    equipments ||--o{ studio_equipments : "一對多"
    
    bookings ||--|| payments : "一對一"
    bookings ||--|| access_codes : "一對一"
    bookings ||--o{ notifications : "一對多"
```

## 關聯關係說明

### 一對一關係 (1:1)
- **users ↔ user_profiles**: 每個使用者對應一份詳細資料
- **bookings ↔ payments**: 每個預約對應一筆支付記錄
- **bookings ↔ access_codes**: 每個預約對應一個門禁QR碼

### 一對多關係 (1:N)
- **users → bookings**: 一個使用者可以有多個預約
- **users → notifications**: 一個使用者可以收到多條通知
- **studios → studio_images**: 一個錄音室可以有多張圖片
- **studios → pricing_plans**: 一個錄音室可以有多種價格方案
- **studios → bookings**: 一個錄音室可以有多個預約
- **bookings → notifications**: 一個預約可以產生多條通知

### 多對多關係 (M:N)
- **studios ↔ equipments**: 透過 studio_equipments 中介表實現
  - 一個錄音室可以有多種設備
  - 一種設備可以在多個錄音室中使用

## 索引設計

### 主要索引
- 所有外鍵欄位都有索引
- 常用查詢欄位有複合索引
- 狀態欄位有索引以提升篩選效能

### 唯一約束
- studio_equipments 表有 (studio_id, equipment_id) 唯一約束
- 避免重複的錄音室-設備關聯

## 資料完整性

### CASCADE 刪除
- 當父記錄被刪除時，相關的子記錄會自動刪除
- 確保資料一致性，避免孤立記錄

### 預設值設定
- 適當的預設值減少資料輸入錯誤
- 狀態欄位有合理的預設狀態
