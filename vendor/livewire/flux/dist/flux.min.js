(()=>{var et=class extends Event{oldState;newState;constructor(t,{oldState:e="",newState:i="",...o}={}){super(t,o),this.oldState=String(e||""),this.newState=String(i||"")}},li=new WeakMap;function pi(t,e,i){li.set(t,setTimeout(()=>{li.has(t)&&t.dispatchEvent(new et("toggle",{cancelable:!1,oldState:e,newState:i}))},0))}var Ct=globalThis.ShadowRoot||function(){},ho=globalThis.HTMLDialogElement||function(){},Xe=new WeakMap,j=new WeakMap,De=new WeakMap;function Ge(t){return De.get(t)||"hidden"}var Je=new WeakMap;function po(t){let e=t.popoverTargetElement;if(!(e instanceof HTMLElement))return;let i=Ge(e);t.popoverTargetAction==="show"&&i==="showing"||t.popoverTargetAction==="hide"&&i==="hidden"||(i==="showing"?Re(e,!0,!0):ue(e,!1)&&(Je.set(e,t),_t(e)))}function ue(t,e){return!(t.popover!=="auto"&&t.popover!=="manual"||!t.isConnected||e&&Ge(t)!=="showing"||!e&&Ge(t)!=="hidden"||t instanceof ho&&t.hasAttribute("open")||document.fullscreenElement===t)}function ci(t){return t?Array.from(j.get(t.ownerDocument)||[]).indexOf(t)+1:0}function bo(t){let e=di(t),i=mo(t);return ci(e)>ci(i)?e:i}function Qe(t){let e=j.get(t);for(let i of e||[])if(!i.isConnected)e.delete(i);else return i;return null}function ye(t){return typeof t.getRootNode=="function"?t.getRootNode():t.parentNode?ye(t.parentNode):t}function di(t){for(;t;){if(t instanceof HTMLElement&&t.popover==="auto"&&De.get(t)==="showing")return t;if(t=t instanceof Element&&t.assignedSlot||t.parentElement||ye(t),t instanceof Ct&&(t=t.host),t instanceof Document)return}}function mo(t){for(;t;){let e=t.popoverTargetElement;if(e instanceof HTMLElement)return e;if(t=t.parentElement||ye(t),t instanceof Ct&&(t=t.host),t instanceof Document)return}}function go(t){let e=new Map,i=0;for(let s of j.get(t.ownerDocument)||[])e.set(s,i),i+=1;e.set(t,i),i+=1;let o=null;function n(s){let r=di(s);if(r===null)return null;let c=e.get(r);(o===null||e.get(o)<c)&&(o=r)}return n(t.parentElement||ye(t)),o}function vo(t){return t.hidden||t instanceof Ct||(t instanceof HTMLButtonElement||t instanceof HTMLInputElement||t instanceof HTMLSelectElement||t instanceof HTMLTextAreaElement||t instanceof HTMLOptGroupElement||t instanceof HTMLOptionElement||t instanceof HTMLFieldSetElement)&&t.disabled||t instanceof HTMLInputElement&&t.type==="hidden"||t instanceof HTMLAnchorElement&&t.href===""?!1:typeof t.tabIndex=="number"&&t.tabIndex!==-1}function wo(t){if(t.shadowRoot&&t.shadowRoot.delegatesFocus!==!0)return null;let e=t;e.shadowRoot&&(e=e.shadowRoot);let i=e.querySelector("[autofocus]");if(i)return i;{let s=e.querySelectorAll("slot");for(let r of s){let c=r.assignedElements({flatten:!0});for(let a of c){if(a.hasAttribute("autofocus"))return a;if(i=a.querySelector("[autofocus]"),i)return i}}}let o=t.ownerDocument.createTreeWalker(e,NodeFilter.SHOW_ELEMENT),n=o.currentNode;for(;n;){if(vo(n))return n;n=o.nextNode()}}function yo(t){wo(t)?.focus()}var Ze=new WeakMap;function _t(t){if(!ue(t,!1))return;let e=t.ownerDocument;if(!t.dispatchEvent(new et("beforetoggle",{cancelable:!0,oldState:"closed",newState:"open"}))||!ue(t,!1))return;let i=!1;if(t.popover==="auto"){let n=t.getAttribute("popover"),s=go(t)||e;if(tt(s,!1,!0),n!==t.getAttribute("popover")||!ue(t,!1))return}Qe(e)||(i=!0),Ze.delete(t);let o=e.activeElement;t.classList.add(":popover-open"),De.set(t,"showing"),Xe.has(e)||Xe.set(e,new Set),Xe.get(e).add(t),yo(t),t.popover==="auto"&&(j.has(e)||j.set(e,new Set),j.get(e).add(t),bi(Je.get(t),!0)),i&&o&&t.popover==="auto"&&Ze.set(t,o),pi(t,"closed","open")}function Re(t,e=!1,i=!1){if(!ue(t,!0))return;let o=t.ownerDocument;if(t.popover==="auto"&&(tt(t,e,i),!ue(t,!0))||(bi(Je.get(t),!1),Je.delete(t),i&&(t.dispatchEvent(new et("beforetoggle",{oldState:"open",newState:"closed"})),!ue(t,!0))))return;Xe.get(o)?.delete(t),j.get(o)?.delete(t),t.classList.remove(":popover-open"),De.set(t,"hidden"),i&&pi(t,"open","closed");let n=Ze.get(t);n&&(Ze.delete(t),e&&n.focus())}function ui(t,e=!1,i=!1){let o=Qe(t);for(;o;)Re(o,e,i),o=Qe(t)}function tt(t,e,i){let o=t.ownerDocument||t;if(t instanceof Document)return ui(o,e,i);let n=null,s=!1;for(let r of j.get(o)||[])if(r===t)s=!0;else if(s){n=r;break}if(!s)return ui(o,e,i);for(;n&&Ge(n)==="showing"&&j.get(o)?.size;)Re(n,e,i)}var St=new WeakMap;function fi(t){if(!t.isTrusted)return;let e=t.composedPath()[0];if(!e)return;let i=e.ownerDocument;if(!Qe(i))return;let n=bo(e);if(n&&t.type==="pointerdown")St.set(i,n);else if(t.type==="pointerup"){let s=St.get(i)===n;St.delete(i),s&&tt(n||i,!1,!0)}}var Et=new WeakMap;function bi(t,e=!1){if(!t)return;Et.has(t)||Et.set(t,t.getAttribute("aria-expanded"));let i=t.popoverTargetElement;if(i instanceof HTMLElement&&i.popover==="auto")t.setAttribute("aria-expanded",String(e));else{let o=Et.get(t);o?t.setAttribute("aria-expanded",o):t.removeAttribute("aria-expanded")}}var hi=globalThis.ShadowRoot||function(){};function Ao(){return typeof HTMLElement<"u"&&typeof HTMLElement.prototype=="object"&&"popover"in HTMLElement.prototype}function ce(t,e,i){let o=t[e];Object.defineProperty(t,e,{value(n){return o.call(this,i(n))}})}var xo=/(^|[^\\]):popover-open\b/g;function So(){return typeof globalThis.CSSLayerBlockRule=="function"}function Eo(){let t=So();return`
${t?"@layer popover-polyfill {":""}
  :where([popover]) {
    position: fixed;
    z-index: 2147483647;
    inset: 0;
    padding: 0.25em;
    width: fit-content;
    height: fit-content;
    border-width: initial;
    border-color: initial;
    border-image: initial;
    border-style: solid;
    background-color: canvas;
    color: canvastext;
    overflow: auto;
    margin: auto;
  }

  :where([popover]:not(.\\:popover-open)) {
    display: none;
  }

  :where(dialog[popover].\\:popover-open) {
    display: block;
  }

  :where(dialog[popover][open]) {
    display: revert;
  }

  :where([anchor].\\:popover-open) {
    inset: auto;
  }

  :where([anchor]:popover-open) {
    inset: auto;
  }

  @supports not (background-color: canvas) {
    :where([popover]) {
      background-color: white;
      color: black;
    }
  }

  @supports (width: -moz-fit-content) {
    :where([popover]) {
      width: -moz-fit-content;
      height: -moz-fit-content;
    }
  }

  @supports not (inset: 0) {
    :where([popover]) {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }
${t?"}":""}
`}var we=null;function kt(t){let e=Eo();if(we===null)try{we=new CSSStyleSheet,we.replaceSync(e)}catch{we=!1}if(we===!1){let i=document.createElement("style");i.textContent=e,t instanceof Document?t.head.prepend(i):t.prepend(i)}else t.adoptedStyleSheets=[we,...t.adoptedStyleSheets]}function ko(){if(typeof window>"u")return;window.ToggleEvent=window.ToggleEvent||et;function t(a){return a?.includes(":popover-open")&&(a=a.replace(xo,"$1.\\:popover-open")),a}ce(Document.prototype,"querySelector",t),ce(Document.prototype,"querySelectorAll",t),ce(Element.prototype,"querySelector",t),ce(Element.prototype,"querySelectorAll",t),ce(Element.prototype,"matches",t),ce(Element.prototype,"closest",t),ce(DocumentFragment.prototype,"querySelectorAll",t),Object.defineProperties(HTMLElement.prototype,{popover:{enumerable:!0,configurable:!0,get(){if(!this.hasAttribute("popover"))return null;let a=(this.getAttribute("popover")||"").toLowerCase();return a===""||a=="auto"?"auto":"manual"},set(a){a===null?this.removeAttribute("popover"):this.setAttribute("popover",a)}},showPopover:{enumerable:!0,configurable:!0,value(){_t(this)}},hidePopover:{enumerable:!0,configurable:!0,value(){Re(this,!0,!0)}},togglePopover:{enumerable:!0,configurable:!0,value(a){De.get(this)==="showing"&&a===void 0||a===!1?Re(this,!0,!0):(a===void 0||a===!0)&&_t(this)}}});let e=Element.prototype.attachShadow;e&&Object.defineProperties(Element.prototype,{attachShadow:{enumerable:!0,configurable:!0,writable:!0,value(a){let l=e.call(this,a);return kt(l),l}}});let i=HTMLElement.prototype.attachInternals;i&&Object.defineProperties(HTMLElement.prototype,{attachInternals:{enumerable:!0,configurable:!0,writable:!0,value(){let a=i.call(this);return a.shadowRoot&&kt(a.shadowRoot),a}}});let o=new WeakMap;function n(a){Object.defineProperties(a.prototype,{popoverTargetElement:{enumerable:!0,configurable:!0,set(l){if(l===null)this.removeAttribute("popovertarget"),o.delete(this);else if(l instanceof Element)this.setAttribute("popovertarget",""),o.set(this,l);else throw new TypeError("popoverTargetElement must be an element or null")},get(){if(this.localName!=="button"&&this.localName!=="input"||this.localName==="input"&&this.type!=="reset"&&this.type!=="image"&&this.type!=="button"||this.disabled||this.form&&this.type==="submit")return null;let l=o.get(this);if(l&&l.isConnected)return l;if(l&&!l.isConnected)return o.delete(this),null;let h=ye(this),f=this.getAttribute("popovertarget");return(h instanceof Document||h instanceof hi)&&f&&h.getElementById(f)||null}},popoverTargetAction:{enumerable:!0,configurable:!0,get(){let l=(this.getAttribute("popovertargetaction")||"").toLowerCase();return l==="show"||l==="hide"?l:"toggle"},set(l){this.setAttribute("popovertargetaction",l)}}})}n(HTMLButtonElement),n(HTMLInputElement);let s=a=>{let l=a.composedPath(),h=l[0];if(!(h instanceof Element)||h?.shadowRoot)return;let f=ye(h);if(!(f instanceof hi||f instanceof Document))return;let b=l.find(m=>m.matches?.("[popovertargetaction],[popovertarget]"));if(b){po(b),a.preventDefault();return}},r=a=>{let l=a.key,h=a.target;!a.defaultPrevented&&h&&(l==="Escape"||l==="Esc")&&tt(h.ownerDocument,!0,!0)};(a=>{a.addEventListener("click",s),a.addEventListener("keydown",r),a.addEventListener("pointerdown",fi),a.addEventListener("pointerup",fi)})(document),kt(document)}Ao()||ko();var at=class extends Event{oldState;newState;constructor(t,{oldState:e="",newState:i="",...o}={}){super(t,o),this.oldState=String(e||""),this.newState=String(i||"")}},mi=new WeakMap;function Ai(t,e,i){mi.set(t,setTimeout(()=>{mi.has(t)&&t.dispatchEvent(new at("toggle",{cancelable:!1,oldState:e,newState:i}))},0))}var Mt=globalThis.ShadowRoot||function(){},_o=globalThis.HTMLDialogElement||function(){},it=new WeakMap,U=new WeakMap,Ne=new WeakMap;function ot(t){return Ne.get(t)||"hidden"}var nt=new WeakMap;function Co(t){let e=t.popoverTargetElement;if(!(e instanceof HTMLElement))return;let i=ot(e);t.popoverTargetAction==="show"&&i==="showing"||t.popoverTargetAction==="hide"&&i==="hidden"||(i==="showing"?Fe(e,!0,!0):he(e,!1)&&(nt.set(e,t),Lt(e)))}function he(t,e){return!(t.popover!=="auto"&&t.popover!=="manual"||!t.isConnected||e&&ot(t)!=="showing"||!e&&ot(t)!=="hidden"||t instanceof _o&&t.hasAttribute("open")||document.fullscreenElement===t)}function gi(t){return t?Array.from(U.get(t.ownerDocument)||[]).indexOf(t)+1:0}function Oo(t){let e=xi(t),i=To(t);return gi(e)>gi(i)?e:i}function st(t){let e=U.get(t);for(let i of e||[])if(!i.isConnected)e.delete(i);else return i;return null}function xe(t){return typeof t.getRootNode=="function"?t.getRootNode():t.parentNode?xe(t.parentNode):t}function xi(t){for(;t;){if(t instanceof HTMLElement&&t.popover==="auto"&&Ne.get(t)==="showing")return t;if(t=t instanceof Element&&t.assignedSlot||t.parentElement||xe(t),t instanceof Mt&&(t=t.host),t instanceof Document)return}}function To(t){for(;t;){let e=t.popoverTargetElement;if(e instanceof HTMLElement)return e;if(t=t.parentElement||xe(t),t instanceof Mt&&(t=t.host),t instanceof Document)return}}function Po(t){let e=new Map,i=0;for(let s of U.get(t.ownerDocument)||[])e.set(s,i),i+=1;e.set(t,i),i+=1;let o=null;function n(s){let r=xi(s);if(r===null)return null;let c=e.get(r);(o===null||e.get(o)<c)&&(o=r)}return n(t.parentElement||xe(t)),o}function Lo(t){return t.hidden||t instanceof Mt||(t instanceof HTMLButtonElement||t instanceof HTMLInputElement||t instanceof HTMLSelectElement||t instanceof HTMLTextAreaElement||t instanceof HTMLOptGroupElement||t instanceof HTMLOptionElement||t instanceof HTMLFieldSetElement)&&t.disabled||t instanceof HTMLInputElement&&t.type==="hidden"||t instanceof HTMLAnchorElement&&t.href===""?!1:typeof t.tabIndex=="number"&&t.tabIndex!==-1}function Mo(t){if(t.shadowRoot&&t.shadowRoot.delegatesFocus!==!0)return null;let e=t;e.shadowRoot&&(e=e.shadowRoot);let i=e.querySelector("[autofocus]");if(i)return i;{let s=e.querySelectorAll("slot");for(let r of s){let c=r.assignedElements({flatten:!0});for(let a of c){if(a.hasAttribute("autofocus"))return a;if(i=a.querySelector("[autofocus]"),i)return i}}}let o=t.ownerDocument.createTreeWalker(e,NodeFilter.SHOW_ELEMENT),n=o.currentNode;for(;n;){if(Lo(n))return n;n=o.nextNode()}}function Ro(t){Mo(t)?.focus()}var rt=new WeakMap;function Lt(t){if(!he(t,!1))return;let e=t.ownerDocument;if(!t.dispatchEvent(new at("beforetoggle",{cancelable:!0,oldState:"closed",newState:"open"}))||!he(t,!1))return;let i=!1;if(t.popover==="auto"){let n=t.getAttribute("popover"),s=Po(t)||e;if(lt(s,!1,!0),n!==t.getAttribute("popover")||!he(t,!1))return}st(e)||(i=!0),rt.delete(t);let o=e.activeElement;t.classList.add(":popover-open"),Ne.set(t,"showing"),it.has(e)||it.set(e,new Set),it.get(e).add(t),Ro(t),t.popover==="auto"&&(U.has(e)||U.set(e,new Set),U.get(e).add(t),Si(nt.get(t),!0)),i&&o&&t.popover==="auto"&&rt.set(t,o),Ai(t,"closed","open")}function Fe(t,e=!1,i=!1){if(!he(t,!0))return;let o=t.ownerDocument;if(t.popover==="auto"&&(lt(t,e,i),!he(t,!0))||(Si(nt.get(t),!1),nt.delete(t),i&&(t.dispatchEvent(new at("beforetoggle",{oldState:"open",newState:"closed"})),!he(t,!0))))return;it.get(o)?.delete(t),U.get(o)?.delete(t),t.classList.remove(":popover-open"),Ne.set(t,"hidden"),i&&Ai(t,"open","closed");let n=rt.get(t);n&&(rt.delete(t),e&&n.focus())}function vi(t,e=!1,i=!1){let o=st(t);for(;o;)Fe(o,e,i),o=st(t)}function lt(t,e,i){let o=t.ownerDocument||t;if(t instanceof Document)return vi(o,e,i);let n=null,s=!1;for(let r of U.get(o)||[])if(r===t)s=!0;else if(s){n=r;break}if(!s)return vi(o,e,i);for(;n&&ot(n)==="showing"&&U.get(o)?.size;)Fe(n,e,i)}var Ot=new WeakMap;function wi(t){if(!t.isTrusted)return;let e=t.composedPath()[0];if(!e)return;let i=e.ownerDocument;if(!st(i))return;let n=Oo(e);if(n&&t.type==="pointerdown")Ot.set(i,n);else if(t.type==="pointerup"){let s=Ot.get(i)===n;Ot.delete(i),s&&lt(n||i,!1,!0)}}var Tt=new WeakMap;function Si(t,e=!1){if(!t)return;Tt.has(t)||Tt.set(t,t.getAttribute("aria-expanded"));let i=t.popoverTargetElement;if(i instanceof HTMLElement&&i.popover==="auto")t.setAttribute("aria-expanded",String(e));else{let o=Tt.get(t);o?t.setAttribute("aria-expanded",o):t.removeAttribute("aria-expanded")}}var yi=globalThis.ShadowRoot||function(){};function Ei(){return typeof HTMLElement<"u"&&typeof HTMLElement.prototype=="object"&&"popover"in HTMLElement.prototype}function ki(){return!!(document.body?.showPopover&&!/native code/i.test(document.body.showPopover.toString()))}function fe(t,e,i){let o=t[e];Object.defineProperty(t,e,{value(n){return o.call(this,i(n))}})}var Do=/(^|[^\\]):popover-open\b/g;function Fo(){return typeof globalThis.CSSLayerBlockRule=="function"}function No(){let t=Fo();return`
${t?"@layer popover-polyfill {":""}
  :where([popover]) {
    position: fixed;
    z-index: 2147483647;
    inset: 0;
    padding: 0.25em;
    width: fit-content;
    height: fit-content;
    border-width: initial;
    border-color: initial;
    border-image: initial;
    border-style: solid;
    background-color: canvas;
    color: canvastext;
    overflow: auto;
    margin: auto;
  }

  :where([popover]:not(.\\:popover-open)) {
    display: none;
  }

  :where(dialog[popover].\\:popover-open) {
    display: block;
  }

  :where(dialog[popover][open]) {
    display: revert;
  }

  :where([anchor].\\:popover-open) {
    inset: auto;
  }

  :where([anchor]:popover-open) {
    inset: auto;
  }

  @supports not (background-color: canvas) {
    :where([popover]) {
      background-color: white;
      color: black;
    }
  }

  @supports (width: -moz-fit-content) {
    :where([popover]) {
      width: -moz-fit-content;
      height: -moz-fit-content;
    }
  }

  @supports not (inset: 0) {
    :where([popover]) {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }
${t?"}":""}
`}var Ae=null;function Pt(t){let e=No();if(Ae===null)try{Ae=new CSSStyleSheet,Ae.replaceSync(e)}catch{Ae=!1}if(Ae===!1){let i=document.createElement("style");i.textContent=e,t instanceof Document?t.head.prepend(i):t.prepend(i)}else t.adoptedStyleSheets=[Ae,...t.adoptedStyleSheets]}function _i(){if(typeof window>"u")return;window.ToggleEvent=window.ToggleEvent||at;function t(a){return a?.includes(":popover-open")&&(a=a.replace(Do,"$1.\\:popover-open")),a}fe(Document.prototype,"querySelector",t),fe(Document.prototype,"querySelectorAll",t),fe(Element.prototype,"querySelector",t),fe(Element.prototype,"querySelectorAll",t),fe(Element.prototype,"matches",t),fe(Element.prototype,"closest",t),fe(DocumentFragment.prototype,"querySelectorAll",t),Object.defineProperties(HTMLElement.prototype,{popover:{enumerable:!0,configurable:!0,get(){if(!this.hasAttribute("popover"))return null;let a=(this.getAttribute("popover")||"").toLowerCase();return a===""||a=="auto"?"auto":"manual"},set(a){a===null?this.removeAttribute("popover"):this.setAttribute("popover",a)}},showPopover:{enumerable:!0,configurable:!0,value(){Lt(this)}},hidePopover:{enumerable:!0,configurable:!0,value(){Fe(this,!0,!0)}},togglePopover:{enumerable:!0,configurable:!0,value(a){Ne.get(this)==="showing"&&a===void 0||a===!1?Fe(this,!0,!0):(a===void 0||a===!0)&&Lt(this)}}});let e=Element.prototype.attachShadow;e&&Object.defineProperties(Element.prototype,{attachShadow:{enumerable:!0,configurable:!0,writable:!0,value(a){let l=e.call(this,a);return Pt(l),l}}});let i=HTMLElement.prototype.attachInternals;i&&Object.defineProperties(HTMLElement.prototype,{attachInternals:{enumerable:!0,configurable:!0,writable:!0,value(){let a=i.call(this);return a.shadowRoot&&Pt(a.shadowRoot),a}}});let o=new WeakMap;function n(a){Object.defineProperties(a.prototype,{popoverTargetElement:{enumerable:!0,configurable:!0,set(l){if(l===null)this.removeAttribute("popovertarget"),o.delete(this);else if(l instanceof Element)this.setAttribute("popovertarget",""),o.set(this,l);else throw new TypeError("popoverTargetElement must be an element or null")},get(){if(this.localName!=="button"&&this.localName!=="input"||this.localName==="input"&&this.type!=="reset"&&this.type!=="image"&&this.type!=="button"||this.disabled||this.form&&this.type==="submit")return null;let l=o.get(this);if(l&&l.isConnected)return l;if(l&&!l.isConnected)return o.delete(this),null;let h=xe(this),f=this.getAttribute("popovertarget");return(h instanceof Document||h instanceof yi)&&f&&h.getElementById(f)||null}},popoverTargetAction:{enumerable:!0,configurable:!0,get(){let l=(this.getAttribute("popovertargetaction")||"").toLowerCase();return l==="show"||l==="hide"?l:"toggle"},set(l){this.setAttribute("popovertargetaction",l)}}})}n(HTMLButtonElement),n(HTMLInputElement);let s=a=>{let l=a.composedPath(),h=l[0];if(!(h instanceof Element)||h?.shadowRoot)return;let f=xe(h);if(!(f instanceof yi||f instanceof Document))return;let b=l.find(m=>m.matches?.("[popovertargetaction],[popovertarget]"));if(b){Co(b),a.preventDefault();return}},r=a=>{let l=a.key,h=a.target;!a.defaultPrevented&&h&&(l==="Escape"||l==="Esc")&&lt(h.ownerDocument,!0,!0)};(a=>{a.addEventListener("click",s),a.addEventListener("keydown",r),a.addEventListener("pointerdown",wi),a.addEventListener("pointerup",wi)})(document),Pt(document)}function Z(t){let e=t({css:(o,...n)=>`@layer base { ${o.raw[0]+n.join("")} }`});if(document.adoptedStyleSheets===void 0){let o=document.createElement("style");o.textContent=e,document.head.appendChild(o);return}let i=new CSSStyleSheet;i.replaceSync(e),document.adoptedStyleSheets=[...document.adoptedStyleSheets,i]}function Oi(t,e){let i=t;for(;i;){if(e(i))return i;i=i.parentElement}}function V(t,e){let i=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,e?{acceptNode:o=>{let n,s;return e(o,{skip:()=>n=!0,reject:()=>s=!0}),n?NodeFilter.FILTER_SKIP:s?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT}}:{});return new Rt(i)}var Rt=class{constructor(e){this.walker=e}from(e){return this.walker.currentNode=e,this}first(){return this.walker.firstChild()}last(){return this.walker.lastChild()}next(e){return this.walker.currentNode=e,this.walker.nextSibling()}nextOrFirst(e){let i=this.next(e);return i||(this.walker.currentNode=this.walker.root,this.first())}prev(e){return this.walker.currentNode=e,this.walker.previousSibling()}prevOrLast(e){let i=this.prev(e);return i||(this.walker.currentNode=this.walker.root,this.last())}closest(e,i){let o=this.from(e).walker;for(;o.currentNode;){if(i(o.currentNode))return o.currentNode;o.parentNode()}}contains(e){return this.find(i=>i===e)}find(e){return this.walk((i,o)=>{e(i)&&o(i)})}findOrFirst(e){return this.find(e)||(this.walker.currentNode=this.walker.root),this.first()}each(e){this.walk(i=>e(i))}some(e){return!!this.find(e)}every(e){let i=!0;return this.walk(o=>{e(o)||(i=!1)}),i}map(e){let i=[];return this.walk(o=>i.push(e(o))),i}filter(e){let i=[];return this.walk(o=>e(o)&&i.push(o)),i}walk(e){let i,o=this.walker,n;for(;o.nextNode()&&(i=o.currentNode,e(i,s=>n=s),n===void 0););return n}};function T(t,e){customElements.define(`ui-${t}`,e)}function w(t,e,i,o={}){return t.addEventListener(e,i,o),{off:()=>t.removeEventListener(e,i),pause:n=>{t.removeEventListener(e,i),n(),t.addEventListener(e,i)}}}function Ti(t){return["a[href]","area[href]","input:not([disabled])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","iframe","object","embed","[tabindex]","[contenteditable]"].some(i=>t.matches(i))&&t.tabIndex>=0}function Io(t,e){let i;return function(){let o=this,n=arguments;i||(t.apply(o,n),i=!0,setTimeout(()=>i=!1,e))}}var ut="pointer";document.addEventListener("keydown",()=>ut="keyboard",{capture:!0});document.addEventListener("pointerdown",t=>{ut=t.pointerType==="mouse"?"mouse":"touch"},{capture:!0});function Dt(){return ut==="keyboard"}function Ho(){return ut==="touch"}function ft(t,e){let i="",o=jo(()=>{i=""},300);t.addEventListener("keydown",n=>{n.key.length===1&&/[a-zA-Z]/.test(n.key)&&(i+=n.key,e(i),n.stopPropagation()),o()})}function Wo(t,e){return"lofi-"+(e?e+"-":"")+Math.random().toString(16).slice(2)}function q(t,e){let i=t.hasAttribute("id")?t.getAttribute("id"):Wo(t,e);return p(t,"id",i),t._x_bindings||(t._x_bindings={}),t._x_bindings.id||(t._x_bindings.id=i),i}function ee(){let t=!1;return e=>(...i)=>{t||(t=!0,e(...i),t=!1)}}function Se(t,e,{gain:i,lose:o,focusable:n,useSafeArea:s}){let r=!1;n&&document.addEventListener("focusin",f=>{Dt()&&(t.contains(f.target)||e.contains(f.target)?(r=!0,i()):(r=!1,o()))});let c=()=>{},a=()=>{},l=()=>{r=!1,o(),c(),a()},h=()=>{r=!1,c(),a()};return t.addEventListener("pointerenter",f=>{Ho()||r||(r=!0,i(),setTimeout(()=>{let{safeArea:b,redraw:m,remove:u}=s?Bo(t,e,f.clientX,f.clientY):zo();c=u;let d,v=Io(g=>{let y=e.getBoundingClientRect(),A=t.getBoundingClientRect(),S;switch(b.contains(g.target)&&Vo(A,y,g.clientX,g.clientY)?S="safeArea":e.contains(g.target)?S="panel":t.contains(g.target)?S="trigger":S="outside",d&&clearTimeout(d),S){case"outside":l();break;case"trigger":m(g.clientX,g.clientY);break;case"panel":c();break;case"safeArea":m(g.clientX,g.clientY),d=setTimeout(()=>{l()},300);break;default:break}},100);document.addEventListener("pointermove",v),a=()=>document.removeEventListener("pointermove",v)}))}),{clear:h}}function Bo(t,e,i,o){let n=document.createElement("div"),s=e.getBoundingClientRect(),r=t.getBoundingClientRect();n.style.position="fixed",p(n,"data-safe-area","");let c=(a,l)=>{if(s.top===0&&s.bottom===0)return;let h;s.left<r.left&&(h="left"),s.right>r.right&&(h="right"),s.top<r.top&&s.bottom<l&&(h="up"),s.bottom>r.bottom&&s.top>l&&(h="down"),h===void 0&&(h="right");let f,b,m,u,d,v,g,y,A=10;switch(h){case"left":f=s.right,b=Math.max(s.right,a)+5,m=b-f,u=Math.min(r.top,s.top)-A,d=Math.max(r.bottom,s.bottom)+A,v=d-u,g=l-u,y=`polygon(0% 0%, 100% ${g}px, 0% 100%)`;break;case"right":f=Math.min(s.left,a)-5,b=s.left,m=b-f,u=Math.min(r.top,s.top)-A,d=Math.max(r.bottom,s.bottom)+A,v=d-u,g=l-u,y=`polygon(0% ${g}px, 100% 0%, 100% 100%)`;break;case"up":f=Math.min(a,s.left)-A,b=Math.max(a,s.right)+A,m=b-f,u=s.bottom,d=Math.max(s.bottom,l)+5,v=d-u,g=a-f,y=`polygon(0% 0%, 100% 0%, ${g}px 100%)`;break;case"down":f=Math.min(a,s.left)-A,b=Math.max(a,s.right)+A,m=b-f,u=Math.min(s.top,l)-5,d=s.top,v=d-u,g=a-f,y=`polygon(${g}px 0%, 100% 100%, 0% 100%)`;break}n.style.left=`${f}px`,n.style.top=`${u}px`,n.style.width=`${m}px`,n.style.height=`${v}px`,n.style.clipPath=y};return{safeArea:n,redraw:(a,l)=>{n.isConnected||t.appendChild(n),c(a,l)},remove:()=>{n.remove()}}}function Vo(t,e,i,o){return!qo(t,i,o)&&!$o(e,i,o)}function qo(t,e,i){return t.left<=e&&e<=t.right&&t.top<=i&&i<=t.bottom}function $o(t,e,i){return t.left<=e&&e<=t.right&&t.top<=i&&i<=t.bottom}function p(t,e,i){t._durableAttributeObserver===void 0&&(t._durableAttributeObserver=Pi(t,[e])),t._durableAttributeObserver.hasAttribute(e)||t._durableAttributeObserver.addAttribute(e),t._durableAttributeObserver.pause(()=>{t.setAttribute(e,i)})}function x(t,e){t._durableAttributeObserver===void 0&&(t._durableAttributeObserver=Pi(t,[e])),t._durableAttributeObserver.hasAttribute(e)||t._durableAttributeObserver.addAttribute(e),t._durableAttributeObserver.pause(()=>{t.removeAttribute(e)})}function Pi(t,e){let i=n=>{n.forEach(s=>{s.oldValue===null?t._durableAttributeObserver.pause(()=>x(t,s.attributeName)):t._durableAttributeObserver.pause(()=>p(t,s.attributeName,s.oldValue))})},o=new MutationObserver(n=>i(n));return o.observe(t,{attributeFilter:e,attributeOldValue:!0}),{attributes:e,hasAttribute(n){return this.attributes.includes(n)},addAttribute(n){this.attributes.includes(n)||this.attributes.push(n),o.observe(t,{attributeFilter:this.attributes,attributeOldValue:!0})},releaseAttribute(n){this.hasAttribute(n)&&o.observe(t,{attributeFilter:this.attributes,attributeOldValue:!0})},pause(n){i(o.takeRecords()),o.disconnect(),n(),o.observe(t,{attributeFilter:this.attributes,attributeOldValue:!0})}}}function zo(){return{safeArea:{contains:()=>!1},redraw:()=>{},remove:()=>{}}}function jo(t,e){let i;return(...o)=>{clearTimeout(i),i=setTimeout(()=>{t(...o)},e)}}var ct=0;function ht(t=!1){if(t)return{lock:()=>{},unlock:()=>{}};let e=()=>{};return{lock(){ct++,!(ct>1)&&(e=Uo(Ci(document.documentElement,"paddingRight",`${window.innerWidth-document.documentElement.clientWidth}px`),Ci(document.documentElement,"overflow","hidden")))},unlock(){ct=Math.max(0,ct-1),e()}}}function Ci(t,e,i){let o=t.style[e];return t.style[e]=i,()=>{t.style[e]=o}}function Uo(...t){return(...e)=>{for(let i of t)i(...e)}}var k=class extends HTMLElement{constructor(){super(),this.boot?.()}connectedCallback(){queueMicrotask(()=>{this.mount?.()})}mixin(e,i={}){return new e(this,i)}appendMixin(e,i={}){return new e(this,i)}use(e){let i;return this.mixins.forEach(o=>{o instanceof e&&(i=o)}),i}uses(e){let i;return this.mixins.forEach(o=>{o instanceof e&&(i=!0)}),!!i}on(e,i){return w(this,e,i)}root(e,i={}){if(e===void 0)return this.__root;let o=document.createElement(e);for(let s in i)setAttribute(o,s,i[s]);let n=this.attachShadow({mode:"open"});return o.appendChild(document.createElement("slot")),n.appendChild(o),this.__root=o,this.__root}},pt=class extends k{};var C=class{constructor(e,i={}){this.el=e,this.grouped=i.grouped===void 0,this.el.mixins=this.el.mixins?this.el.mixins:new Map,this.el.mixins.set(this.constructor.name,this),this.el[this.constructor.name]=!0,this.el.use||(this.el.use=k.prototype.use.bind(this.el)),this.opts=i,this.boot?.({options:o=>{let n=o;Object.entries(this.opts).forEach(([s,r])=>{r!==void 0&&(n[s]=r)}),this.opts=n}}),queueMicrotask(()=>{this.mount?.()})}options(){return this.opts}hasGroup(){return!!this.group()}group(){if(this.grouped!==!1)return Oi(this.el,e=>e[this.groupedByType.name])?.use(this.groupedByType)}on(e,i){return w(this.el,e,i)}},$=class extends C{constructor(e,i={}){super(e,i)}walker(){return V(this.el,(e,{skip:i,reject:o})=>{if(e[this.constructor.name]&&e!==this.el)return o();if(!e[this.groupOfType.name]||!e.mixins.get(this.groupOfType.name).grouped)return i()})}};var z=class extends C{boot({options:e}){this.initialState=this.el.value,this.getterFunc=()=>{},this.setterFunc=i=>this.initialState=i,Object.defineProperty(this.el,"value",{get:()=>this.getterFunc(),set:i=>{this.setterFunc(i)}})}initial(e){e(this.initialState)}getter(e){this.getterFunc=e}setter(e){this.setterFunc=e}dispatch(){this.el.dispatchEvent(new Event("input",{bubbles:!1,cancelable:!0})),this.el.dispatchEvent(new Event("change",{bubbles:!1,cancelable:!0}))}};var Y=new Map,R=class extends C{boot({options:e}){e({triggers:[],scope:null});let i=this.options().scope||"global";p(this.el,"popover","manual"),this.triggers=this.options().triggers,this.onChanges=[],this.state=!1,w(this.el,"beforetoggle",o=>{let n=this.state;if(this.state=o.newState==="open",this.state){Yo(this.el,i);let s=new AbortController,r=document.activeElement,c=[...this.triggers,r];setTimeout(()=>{Ko(this.el,c,s),Xo(this.el,c,s),Go(this.el,c,s)}),this.el.addEventListener("beforetoggle",a=>{a.newState==="closed"&&(s.abort(),r?.focus())},{signal:s.signal})}n!==this.state&&this.onChanges.forEach(s=>s(this.state,n))}),w(this.el,"toggle",o=>{if(o.newState==="open")Y.has(i)||Y.set(i,new Set),Y.get(i).add(this.el);else if(o.newState==="closed"){if(!Y.has(i))return;Y.get(i).delete(this.el),Y.get(i).size===0&&Y.delete(i)}})}onChange(e){this.onChanges.push(e)}setState(e){e?this.show():this.hide()}getState(){return this.state}toggle(){this.el.togglePopover()}show(){this.el.showPopover()}hide(){this.el.hidePopover()}};function Yo(t,e){Y.has(e)&&Y.get(e).forEach(i=>{t.contains(i)||i.contains(t)||i.hidePopover()})}function Ko(t,e,i){document.addEventListener("click",o=>{t.contains(o.target)||e.includes(o.target)||t.hidePopover()},{signal:i.signal})}function Xo(t,e,i){document.addEventListener("focusin",o=>{t.contains(o.target)||e.includes(o.target)||(i.abort(),t.hidePopover())},{capture:!0,signal:i.signal})}function Go(t,e,i){document.addEventListener("keydown",o=>{o.key==="Escape"&&t.hidePopover()},{signal:i.signal})}var te=Math.min,P=Math.max,He=Math.round,We=Math.floor,I=t=>({x:t,y:t}),Jo={left:"right",right:"left",bottom:"top",top:"bottom"},Qo={start:"end",end:"start"};function Ft(t,e,i){return P(t,te(e,i))}function Ee(t,e){return typeof t=="function"?t(e):t}function K(t){return t.split("-")[0]}function ke(t){return t.split("-")[1]}function Nt(t){return t==="x"?"y":"x"}function It(t){return t==="y"?"height":"width"}function ie(t){return["top","bottom"].includes(K(t))?"y":"x"}function Ht(t){return Nt(ie(t))}function Li(t,e,i){i===void 0&&(i=!1);let o=ke(t),n=Ht(t),s=It(n),r=n==="x"?o===(i?"end":"start")?"right":"left":o==="start"?"bottom":"top";return e.reference[s]>e.floating[s]&&(r=Ie(r)),[r,Ie(r)]}function Mi(t){let e=Ie(t);return[dt(t),e,dt(e)]}function dt(t){return t.replace(/start|end/g,e=>Qo[e])}function Zo(t,e,i){let o=["left","right"],n=["right","left"],s=["top","bottom"],r=["bottom","top"];switch(t){case"top":case"bottom":return i?e?n:o:e?o:n;case"left":case"right":return e?s:r;default:return[]}}function Ri(t,e,i,o){let n=ke(t),s=Zo(K(t),i==="start",o);return n&&(s=s.map(r=>r+"-"+n),e&&(s=s.concat(s.map(dt)))),s}function Ie(t){return t.replace(/left|right|bottom|top/g,e=>Jo[e])}function en(t){return{top:0,right:0,bottom:0,left:0,...t}}function Di(t){return typeof t!="number"?en(t):{top:t,right:t,bottom:t,left:t}}function pe(t){let{x:e,y:i,width:o,height:n}=t;return{width:o,height:n,top:i,left:e,right:e+o,bottom:i+n,x:e,y:i}}function Fi(t,e,i){let{reference:o,floating:n}=t,s=ie(e),r=Ht(e),c=It(r),a=K(e),l=s==="y",h=o.x+o.width/2-n.width/2,f=o.y+o.height/2-n.height/2,b=o[c]/2-n[c]/2,m;switch(a){case"top":m={x:h,y:o.y-n.height};break;case"bottom":m={x:h,y:o.y+o.height};break;case"right":m={x:o.x+o.width,y:f};break;case"left":m={x:o.x-n.width,y:f};break;default:m={x:o.x,y:o.y}}switch(ke(e)){case"start":m[r]-=b*(i&&l?-1:1);break;case"end":m[r]+=b*(i&&l?-1:1);break}return m}var Ni=async(t,e,i)=>{let{placement:o="bottom",strategy:n="absolute",middleware:s=[],platform:r}=i,c=s.filter(Boolean),a=await(r.isRTL==null?void 0:r.isRTL(e)),l=await r.getElementRects({reference:t,floating:e,strategy:n}),{x:h,y:f}=Fi(l,o,a),b=o,m={},u=0;for(let d=0;d<c.length;d++){let{name:v,fn:g}=c[d],{x:y,y:A,data:S,reset:E}=await g({x:h,y:f,initialPlacement:o,placement:b,strategy:n,middlewareData:m,rects:l,platform:r,elements:{reference:t,floating:e}});h=y??h,f=A??f,m={...m,[v]:{...m[v],...S}},E&&u<=50&&(u++,typeof E=="object"&&(E.placement&&(b=E.placement),E.rects&&(l=E.rects===!0?await r.getElementRects({reference:t,floating:e,strategy:n}):E.rects),{x:h,y:f}=Fi(l,b,a)),d=-1)}return{x:h,y:f,placement:b,strategy:n,middlewareData:m}};async function bt(t,e){var i;e===void 0&&(e={});let{x:o,y:n,platform:s,rects:r,elements:c,strategy:a}=t,{boundary:l="clippingAncestors",rootBoundary:h="viewport",elementContext:f="floating",altBoundary:b=!1,padding:m=0}=Ee(e,t),u=Di(m),v=c[b?f==="floating"?"reference":"floating":f],g=pe(await s.getClippingRect({element:(i=await(s.isElement==null?void 0:s.isElement(v)))==null||i?v:v.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(c.floating)),boundary:l,rootBoundary:h,strategy:a})),y=f==="floating"?{x:o,y:n,width:r.floating.width,height:r.floating.height}:r.reference,A=await(s.getOffsetParent==null?void 0:s.getOffsetParent(c.floating)),S=await(s.isElement==null?void 0:s.isElement(A))?await(s.getScale==null?void 0:s.getScale(A))||{x:1,y:1}:{x:1,y:1},E=pe(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:y,offsetParent:A,strategy:a}):y);return{top:(g.top-E.top+u.top)/S.y,bottom:(E.bottom-g.bottom+u.bottom)/S.y,left:(g.left-E.left+u.left)/S.x,right:(E.right-g.right+u.right)/S.x}}var Ii=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var i,o;let{placement:n,middlewareData:s,rects:r,initialPlacement:c,platform:a,elements:l}=e,{mainAxis:h=!0,crossAxis:f=!0,fallbackPlacements:b,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:u="none",flipAlignment:d=!0,...v}=Ee(t,e);if((i=s.arrow)!=null&&i.alignmentOffset)return{};let g=K(n),y=ie(c),A=K(c)===c,S=await(a.isRTL==null?void 0:a.isRTL(l.floating)),E=b||(A||!d?[Ie(c)]:Mi(c)),se=u!=="none";!b&&se&&E.push(...Ri(c,d,u,S));let B=[c,...E],re=await bt(e,v),ae=[],G=((o=s.flip)==null?void 0:o.overflows)||[];if(h&&ae.push(re[g]),f){let le=Li(n,r,S);ae.push(re[le[0]],re[le[1]])}if(G=[...G,{placement:n,overflows:ae}],!ae.every(le=>le<=0)){var Te,Pe;let le=(((Te=s.flip)==null?void 0:Te.index)||0)+1,ai=B[le];if(ai)return{data:{index:le,overflows:G},reset:{placement:ai}};let Me=(Pe=G.filter(ve=>ve.overflows[0]<=0).sort((ve,J)=>ve.overflows[1]-J.overflows[1])[0])==null?void 0:Pe.placement;if(!Me)switch(m){case"bestFit":{var Le;let ve=(Le=G.filter(J=>{if(se){let Q=ie(J.placement);return Q===y||Q==="y"}return!0}).map(J=>[J.placement,J.overflows.filter(Q=>Q>0).reduce((Q,fo)=>Q+fo,0)]).sort((J,Q)=>J[1]-Q[1])[0])==null?void 0:Le[0];ve&&(Me=ve);break}case"initialPlacement":Me=c;break}if(n!==Me)return{reset:{placement:Me}}}return{}}}};async function tn(t,e){let{placement:i,platform:o,elements:n}=t,s=await(o.isRTL==null?void 0:o.isRTL(n.floating)),r=K(i),c=ke(i),a=ie(i)==="y",l=["left","top"].includes(r)?-1:1,h=s&&a?-1:1,f=Ee(e,t),{mainAxis:b,crossAxis:m,alignmentAxis:u}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return c&&typeof u=="number"&&(m=c==="end"?u*-1:u),a?{x:m*h,y:b*l}:{x:b*l,y:m*h}}var Hi=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(e){var i,o;let{x:n,y:s,placement:r,middlewareData:c}=e,a=await tn(e,t);return r===((i=c.offset)==null?void 0:i.placement)&&(o=c.arrow)!=null&&o.alignmentOffset?{}:{x:n+a.x,y:s+a.y,data:{...a,placement:r}}}}},Wi=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){let{x:i,y:o,placement:n}=e,{mainAxis:s=!0,crossAxis:r=!1,limiter:c={fn:v=>{let{x:g,y}=v;return{x:g,y}}},...a}=Ee(t,e),l={x:i,y:o},h=await bt(e,a),f=ie(K(n)),b=Nt(f),m=l[b],u=l[f];if(s){let v=b==="y"?"top":"left",g=b==="y"?"bottom":"right",y=m+h[v],A=m-h[g];m=Ft(y,m,A)}if(r){let v=f==="y"?"top":"left",g=f==="y"?"bottom":"right",y=u+h[v],A=u-h[g];u=Ft(y,u,A)}let d=c.fn({...e,[b]:m,[f]:u});return{...d,data:{x:d.x-i,y:d.y-o,enabled:{[b]:s,[f]:r}}}}}};var Bi=function(t){return t===void 0&&(t={}),{name:"size",options:t,async fn(e){var i,o;let{placement:n,rects:s,platform:r,elements:c}=e,{apply:a=()=>{},...l}=Ee(t,e),h=await bt(e,l),f=K(n),b=ke(n),m=ie(n)==="y",{width:u,height:d}=s.floating,v,g;f==="top"||f==="bottom"?(v=f,g=b===(await(r.isRTL==null?void 0:r.isRTL(c.floating))?"start":"end")?"left":"right"):(g=f,v=b==="end"?"top":"bottom");let y=d-h.top-h.bottom,A=u-h.left-h.right,S=te(d-h[v],y),E=te(u-h[g],A),se=!e.middlewareData.shift,B=S,re=E;if((i=e.middlewareData.shift)!=null&&i.enabled.x&&(re=A),(o=e.middlewareData.shift)!=null&&o.enabled.y&&(B=y),se&&!b){let G=P(h.left,0),Te=P(h.right,0),Pe=P(h.top,0),Le=P(h.bottom,0);m?re=u-2*(G!==0||Te!==0?G+Te:P(h.left,h.right)):B=d-2*(Pe!==0||Le!==0?Pe+Le:P(h.top,h.bottom))}await a({...e,availableWidth:re,availableHeight:B});let ae=await r.getDimensions(c.floating);return u!==ae.width||d!==ae.height?{reset:{rects:!0}}:{}}}};function mt(){return typeof window<"u"}function de(t){return qi(t)?(t.nodeName||"").toLowerCase():"#document"}function M(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function H(t){var e;return(e=(qi(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function qi(t){return mt()?t instanceof Node||t instanceof M(t).Node:!1}function D(t){return mt()?t instanceof Element||t instanceof M(t).Element:!1}function W(t){return mt()?t instanceof HTMLElement||t instanceof M(t).HTMLElement:!1}function Vi(t){return!mt()||typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof M(t).ShadowRoot}function Ce(t){let{overflow:e,overflowX:i,overflowY:o,display:n}=F(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+i)&&!["inline","contents"].includes(n)}function $i(t){return["table","td","th"].includes(de(t))}function Be(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch{return!1}})}function gt(t){let e=vt(),i=D(t)?F(t):t;return["transform","translate","scale","rotate","perspective"].some(o=>i[o]?i[o]!=="none":!1)||(i.containerType?i.containerType!=="normal":!1)||!e&&(i.backdropFilter?i.backdropFilter!=="none":!1)||!e&&(i.filter?i.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(i.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(i.contain||"").includes(o))}function zi(t){let e=X(t);for(;W(e)&&!be(e);){if(gt(e))return e;if(Be(e))return null;e=X(e)}return null}function vt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function be(t){return["html","body","#document"].includes(de(t))}function F(t){return M(t).getComputedStyle(t)}function Ve(t){return D(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function X(t){if(de(t)==="html")return t;let e=t.assignedSlot||t.parentNode||Vi(t)&&t.host||H(t);return Vi(e)?e.host:e}function ji(t){let e=X(t);return be(e)?t.ownerDocument?t.ownerDocument.body:t.body:W(e)&&Ce(e)?e:ji(e)}function _e(t,e,i){var o;e===void 0&&(e=[]),i===void 0&&(i=!0);let n=ji(t),s=n===((o=t.ownerDocument)==null?void 0:o.body),r=M(n);if(s){let c=wt(r);return e.concat(r,r.visualViewport||[],Ce(n)?n:[],c&&i?_e(c):[])}return e.concat(n,_e(n,[],i))}function wt(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function Ki(t){let e=F(t),i=parseFloat(e.width)||0,o=parseFloat(e.height)||0,n=W(t),s=n?t.offsetWidth:i,r=n?t.offsetHeight:o,c=He(i)!==s||He(o)!==r;return c&&(i=s,o=r),{width:i,height:o,$:c}}function Bt(t){return D(t)?t:t.contextElement}function Oe(t){let e=Bt(t);if(!W(e))return I(1);let i=e.getBoundingClientRect(),{width:o,height:n,$:s}=Ki(e),r=(s?He(i.width):i.width)/o,c=(s?He(i.height):i.height)/n;return(!r||!Number.isFinite(r))&&(r=1),(!c||!Number.isFinite(c))&&(c=1),{x:r,y:c}}var on=I(0);function Xi(t){let e=M(t);return!vt()||!e.visualViewport?on:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function nn(t,e,i){return e===void 0&&(e=!1),!i||e&&i!==M(t)?!1:e}function me(t,e,i,o){e===void 0&&(e=!1),i===void 0&&(i=!1);let n=t.getBoundingClientRect(),s=Bt(t),r=I(1);e&&(o?D(o)&&(r=Oe(o)):r=Oe(t));let c=nn(s,i,o)?Xi(s):I(0),a=(n.left+c.x)/r.x,l=(n.top+c.y)/r.y,h=n.width/r.x,f=n.height/r.y;if(s){let b=M(s),m=o&&D(o)?M(o):o,u=b,d=wt(u);for(;d&&o&&m!==u;){let v=Oe(d),g=d.getBoundingClientRect(),y=F(d),A=g.left+(d.clientLeft+parseFloat(y.paddingLeft))*v.x,S=g.top+(d.clientTop+parseFloat(y.paddingTop))*v.y;a*=v.x,l*=v.y,h*=v.x,f*=v.y,a+=A,l+=S,u=M(d),d=wt(u)}}return pe({width:h,height:f,x:a,y:l})}function Vt(t,e){let i=Ve(t).scrollLeft;return e?e.left+i:me(H(t)).left+i}function Gi(t,e,i){i===void 0&&(i=!1);let o=t.getBoundingClientRect(),n=o.left+e.scrollLeft-(i?0:Vt(t,o)),s=o.top+e.scrollTop;return{x:n,y:s}}function sn(t){let{elements:e,rect:i,offsetParent:o,strategy:n}=t,s=n==="fixed",r=H(o),c=e?Be(e.floating):!1;if(o===r||c&&s)return i;let a={scrollLeft:0,scrollTop:0},l=I(1),h=I(0),f=W(o);if((f||!f&&!s)&&((de(o)!=="body"||Ce(r))&&(a=Ve(o)),W(o))){let m=me(o);l=Oe(o),h.x=m.x+o.clientLeft,h.y=m.y+o.clientTop}let b=r&&!f&&!s?Gi(r,a,!0):I(0);return{width:i.width*l.x,height:i.height*l.y,x:i.x*l.x-a.scrollLeft*l.x+h.x+b.x,y:i.y*l.y-a.scrollTop*l.y+h.y+b.y}}function rn(t){return Array.from(t.getClientRects())}function an(t){let e=H(t),i=Ve(t),o=t.ownerDocument.body,n=P(e.scrollWidth,e.clientWidth,o.scrollWidth,o.clientWidth),s=P(e.scrollHeight,e.clientHeight,o.scrollHeight,o.clientHeight),r=-i.scrollLeft+Vt(t),c=-i.scrollTop;return F(o).direction==="rtl"&&(r+=P(e.clientWidth,o.clientWidth)-n),{width:n,height:s,x:r,y:c}}function ln(t,e){let i=M(t),o=H(t),n=i.visualViewport,s=o.clientWidth,r=o.clientHeight,c=0,a=0;if(n){s=n.width,r=n.height;let l=vt();(!l||l&&e==="fixed")&&(c=n.offsetLeft,a=n.offsetTop)}return{width:s,height:r,x:c,y:a}}function cn(t,e){let i=me(t,!0,e==="fixed"),o=i.top+t.clientTop,n=i.left+t.clientLeft,s=W(t)?Oe(t):I(1),r=t.clientWidth*s.x,c=t.clientHeight*s.y,a=n*s.x,l=o*s.y;return{width:r,height:c,x:a,y:l}}function Ui(t,e,i){let o;if(e==="viewport")o=ln(t,i);else if(e==="document")o=an(H(t));else if(D(e))o=cn(e,i);else{let n=Xi(t);o={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return pe(o)}function Ji(t,e){let i=X(t);return i===e||!D(i)||be(i)?!1:F(i).position==="fixed"||Ji(i,e)}function un(t,e){let i=e.get(t);if(i)return i;let o=_e(t,[],!1).filter(c=>D(c)&&de(c)!=="body"),n=null,s=F(t).position==="fixed",r=s?X(t):t;for(;D(r)&&!be(r);){let c=F(r),a=gt(r);!a&&c.position==="fixed"&&(n=null),(s?!a&&!n:!a&&c.position==="static"&&!!n&&["absolute","fixed"].includes(n.position)||Ce(r)&&!a&&Ji(t,r))?o=o.filter(h=>h!==r):n=c,r=X(r)}return e.set(t,o),o}function fn(t){let{element:e,boundary:i,rootBoundary:o,strategy:n}=t,r=[...i==="clippingAncestors"?Be(e)?[]:un(e,this._c):[].concat(i),o],c=r[0],a=r.reduce((l,h)=>{let f=Ui(e,h,n);return l.top=P(f.top,l.top),l.right=te(f.right,l.right),l.bottom=te(f.bottom,l.bottom),l.left=P(f.left,l.left),l},Ui(e,c,n));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function hn(t){let{width:e,height:i}=Ki(t);return{width:e,height:i}}function pn(t,e,i){let o=W(e),n=H(e),s=i==="fixed",r=me(t,!0,s,e),c={scrollLeft:0,scrollTop:0},a=I(0);if(o||!o&&!s)if((de(e)!=="body"||Ce(n))&&(c=Ve(e)),o){let b=me(e,!0,s,e);a.x=b.x+e.clientLeft,a.y=b.y+e.clientTop}else n&&(a.x=Vt(n));let l=n&&!o&&!s?Gi(n,c):I(0),h=r.left+c.scrollLeft-a.x-l.x,f=r.top+c.scrollTop-a.y-l.y;return{x:h,y:f,width:r.width,height:r.height}}function Wt(t){return F(t).position==="static"}function Yi(t,e){if(!W(t)||F(t).position==="fixed")return null;if(e)return e(t);let i=t.offsetParent;return H(t)===i&&(i=i.ownerDocument.body),i}function Qi(t,e){let i=M(t);if(Be(t))return i;if(!W(t)){let n=X(t);for(;n&&!be(n);){if(D(n)&&!Wt(n))return n;n=X(n)}return i}let o=Yi(t,e);for(;o&&$i(o)&&Wt(o);)o=Yi(o,e);return o&&be(o)&&Wt(o)&&!gt(o)?i:o||zi(t)||i}var dn=async function(t){let e=this.getOffsetParent||Qi,i=this.getDimensions,o=await i(t.floating);return{reference:pn(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function bn(t){return F(t).direction==="rtl"}var mn={convertOffsetParentRelativeRectToViewportRelativeRect:sn,getDocumentElement:H,getClippingRect:fn,getOffsetParent:Qi,getElementRects:dn,getClientRects:rn,getDimensions:hn,getScale:Oe,isElement:D,isRTL:bn};function Zi(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function gn(t,e){let i=null,o,n=H(t);function s(){var c;clearTimeout(o),(c=i)==null||c.disconnect(),i=null}function r(c,a){c===void 0&&(c=!1),a===void 0&&(a=1),s();let l=t.getBoundingClientRect(),{left:h,top:f,width:b,height:m}=l;if(c||e(),!b||!m)return;let u=We(f),d=We(n.clientWidth-(h+b)),v=We(n.clientHeight-(f+m)),g=We(h),A={rootMargin:-u+"px "+-d+"px "+-v+"px "+-g+"px",threshold:P(0,te(1,a))||1},S=!0;function E(se){let B=se[0].intersectionRatio;if(B!==a){if(!S)return r();B?r(!1,B):o=setTimeout(()=>{r(!1,1e-7)},1e3)}B===1&&!Zi(l,t.getBoundingClientRect())&&r(),S=!1}try{i=new IntersectionObserver(E,{...A,root:n.ownerDocument})}catch{i=new IntersectionObserver(E,A)}i.observe(t)}return r(!0),s}function eo(t,e,i,o){o===void 0&&(o={});let{ancestorScroll:n=!0,ancestorResize:s=!0,elementResize:r=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:a=!1}=o,l=Bt(t),h=n||s?[...l?_e(l):[],..._e(e)]:[];h.forEach(g=>{n&&g.addEventListener("scroll",i,{passive:!0}),s&&g.addEventListener("resize",i)});let f=l&&c?gn(l,i):null,b=-1,m=null;r&&(m=new ResizeObserver(g=>{let[y]=g;y&&y.target===l&&m&&(m.unobserve(e),cancelAnimationFrame(b),b=requestAnimationFrame(()=>{var A;(A=m)==null||A.observe(e)})),i()}),l&&!a&&m.observe(l),m.observe(e));let u,d=a?me(t):null;a&&v();function v(){let g=me(t);d&&!Zi(d,g)&&i(),d=g,u=requestAnimationFrame(v)}return i(),()=>{var g;h.forEach(y=>{n&&y.removeEventListener("scroll",i),s&&y.removeEventListener("resize",i)}),f?.(),(g=m)==null||g.disconnect(),m=null,a&&cancelAnimationFrame(u)}}var to=Hi;var io=Wi,oo=Ii,no=Bi;var so=(t,e,i)=>{let o=new Map,n={platform:mn,...i},s={...n.platform,_c:o};return Ni(t,e,{...n,platform:s})};var N=class extends C{boot({options:e}){if(e({reference:null,auto:!0,position:"bottom start",gap:"5",offset:"0",matchWidth:!1,crossAxis:!1}),this.options().reference===null||this.options().position===null)return;let[i,o]=yn(this.el),n=vn(this.el,this.options().reference,i,{position:this.options().position,gap:this.options().gap,offset:this.options().offset,matchWidth:this.options().matchWidth,crossAxis:this.options().crossAxis}),s=()=>{};this.reposition=(...r)=>{this.options().auto?s=eo(this.options().reference,this.el,n):n(null,...r)},this.cleanup=()=>{s(),o()}}};function vn(t,e,i,{position:o,offset:n,gap:s,matchWidth:r,crossAxis:c}){return(a,l,h)=>{so(e,t,{placement:wn(o),middleware:[to({mainAxis:Number(s),alignmentAxis:Number(n)}),oo(),io({padding:5,crossAxis:c}),no({padding:5,apply({rects:f,elements:b,availableHeight:m}){r&&Object.assign(b.floating.style,{width:`${f.reference.width}px`}),b.floating.style.maxHeight=m>=b.floating.scrollHeight?"":`${m}px`}})]}).then(({x:f,y:b})=>{i(l||f,h||b)})}}function wn(t){return t.split(" ").join("-")}function yn(t){let e=(s,r)=>{Object.assign(t.style,{position:"absolute",overflowY:"auto",left:`${s}px`,top:`${r}px`,right:"auto",bottom:"auto"})},i,o,n=new MutationObserver(()=>e(i,o));return[(s,r)=>{i=s,o=r,n.disconnect(),e(i,o),n.observe(t,{attributeFilter:["style"]})},()=>{n.disconnect()}]}var qt=class extends k{boot(){let e=this.trigger(),i=this.overlay();if(e){if(!i)return console.warn("ui-dropdown: no [popover] overlay found",this)}else return console.warn("ui-dropdown: no trigger element found",this);if(this._disabled=this.hasAttribute("disabled"),this._controllable=new z(this),i._popoverable=new R(i),i._anchorable=new N(i,{reference:e,position:this.hasAttribute("position")?this.getAttribute("position"):void 0,gap:this.hasAttribute("gap")?this.getAttribute("gap"):void 0,offset:this.hasAttribute("offset")?this.getAttribute("offset"):void 0}),i._popoverable.onChange(()=>{i._popoverable.getState()?i._anchorable.reposition():i._anchorable.cleanup()}),["ui-menu","ui-context"].includes(i.localName)){let{lock:s,unlock:r}=ht();i._popoverable.onChange(()=>{i._popoverable.getState()?s():r()})}this._controllable.initial(s=>i._popoverable.setState(s)),this._controllable.getter(()=>i._popoverable.getState());let o=ee();this._controllable.setter(s=>i._popoverable.setState(s)),i._popoverable.onChange(o(()=>this._controllable.dispatch())),this.hasAttribute("hover")&&Se(e,i,{gain(){i._popoverable.setState(!0)},lose(){i._popoverable.setState(!1)},focusable:!0}),w(e,"click",()=>i._popoverable.toggle()),i._popoverable.getState()?(p(this,"data-open",""),p(e,"data-open",""),p(i,"data-open","")):(x(this,"data-open"),x(e,"data-open"),x(i,"data-open")),i._popoverable.onChange(()=>{i._popoverable.getState()?(p(this,"data-open",""),p(e,"data-open",""),p(i,"data-open","")):(x(this,"data-open"),x(e,"data-open"),x(i,"data-open"))});let n=q(i,"dropdown");p(e,"aria-haspopup","true"),p(e,"aria-controls",n),p(e,"aria-expanded",i._popoverable.getState()?"true":"false"),i._popoverable.onChange(()=>{p(e,"aria-expanded",i._popoverable.getState()?"true":"false")}),i._popoverable.onChange(()=>{setTimeout(()=>i._popoverable.getState()?i.onPopoverShow?.():i.onPopoverHide?.())})}trigger(){return this.querySelector("button")}overlay(){return this.lastElementChild?.matches("[popover]")&&this.lastElementChild}};T("dropdown",qt);var $t=class extends k{boot(){let e=this.hasAttribute("label")?"label":"description",i=this.button(),o=this.overlay();if(i){if(!o)return}else return console.warn("ui-tooltip: no trigger element found",this);this._disabled=this.hasAttribute("disabled"),o._popoverable=new R(o,{scope:"tooltip"}),o._anchorable=new N(o,{reference:i,position:this.hasAttribute("position")?this.getAttribute("position"):void 0,gap:this.hasAttribute("gap")?this.getAttribute("gap"):void 0,offset:this.hasAttribute("offset")?this.getAttribute("offset"):void 0}),o._popoverable.onChange(()=>{o._popoverable.getState()?o._anchorable.reposition():o._anchorable.cleanup()}),this._disabled||Se(i,o,{gain(){o._popoverable.setState(!0)},lose(){o._popoverable.setState(!1)},focusable:!0,useSafeArea:!1});let n=q(o,"tooltip"),s=this.hasAttribute("interactive"),r=this.hasAttribute("label")||i.textContent.trim()==="";s?(p(i,"aria-controls",n),p(i,"aria-expanded","false"),o._popoverable.onChange(()=>{o._popoverable.getState()?p(i,"aria-expanded","true"):p(i,"aria-expanded","false")})):(r?p(i,"aria-labelledby",n):p(i,"aria-describedby",n),p(o,"aria-hidden","true")),p(o,"role","tooltip")}button(){return this.firstElementChild}overlay(){return this.lastElementChild!==this.button()&&this.lastElementChild}};T("tooltip",$t);var oe=class extends ${groupOfType=_;boot({options:e}){e({multiple:!1}),this.state=this.options().multiple?new Set:null,this.onChanges=[]}onInitAndChange(e){e(),this.onChanges.push(e)}onChange(e){this.onChanges.push(e)}changed(e,i=!1){if(e.ungrouped)return;let o=e.value,n=e.isSelected(),s=this.options().multiple;n?s?this.state.add(o):this.state=o:s?this.state.delete(o):this.state=null,i||this.onChanges.forEach(r=>r())}getState(){return this.options().multiple?Array.from(this.state):this.state}hasValue(e){return this.options().multiple?this.state.has(e):this.state===e}setState(e){(e===null||e==="")&&(e=this.options().multiple?[]:""),this.options().multiple?(Array.isArray(e)||(e=[e]),e=e.map(o=>o+"")):e=e+"",this.state=this.options().multiple?new Set(e):e;let i=this.options().multiple?e:[e];this.walker().each(o=>{let n=o.use(_);if(n.ungrouped)return;let s=i.includes(n.value);s&&!n.isSelected()?n.surgicallySelect():!s&&n.isSelected()&&n.surgicallyDeselect()}),this.onChanges.forEach(o=>o())}selected(){return this.walker().find(e=>e.use(_).isSelected()).use(_)}selecteds(){return this.walker().filter(e=>e.use(_).isSelected()).map(e=>e.use(_))}selectFirst(){this.walker().first()?.use(_).select()}selectAll(){this.walker().filter(e=>!e.use(_).isSelected()).map(e=>e.use(_).select())}deselectAll(){this.walker().filter(e=>e.use(_).isSelected()).map(e=>e.use(_).deselect())}allAreSelected(){return this.walker().filter(e=>e.use(_).isSelected()).length===this.walker().filter(e=>!0).length}noneAreSelected(){return this.state===null||this.state?.size===0}selectableByValue(e){return this.walker().find(i=>i.use(_).value===e)?.use(_)}deselectOthers(e){this.walker().each(i=>{i!==e&&i.use(_).surgicallyDeselect()})}selectedTextValue(){return this.options().multiple?Array.from(this.state).map(e=>this.convertValueStringToElementText(e)).join(", "):this.convertValueStringToElementText(this.state)}convertValueStringToElementText(e){let i=this.findByValue(e);return i?i.label||i.value:e}findByValue(e){return this.selecteds().find(i=>i.value===e)}walker(){return V(this.el,(e,{skip:i,reject:o})=>{if(e[this.constructor.name]&&e!==this.el)return o();if(!e[this.groupOfType.name]||e.mixins.get(this.groupOfType.name).ungrouped)return i()})}},_=class extends C{boot({options:e}){this.groupedByType=oe,e({ungrouped:!1,togglable:!1,value:void 0,label:void 0,selectedInitially:!1,dataAttr:"data-selected",ariaAttr:"aria-selected"}),this.ungrouped=this.options().ungrouped,this.value=this.options().value===void 0?this.el.value:this.options().value,this.value=this.value+"",this.label=this.options().label;let i=this.options().selectedInitially;this.group()&&this.group().hasValue(this.value)&&(i=!0),this.multiple=this.hasGroup()?this.group().options().multiple:!1,this.toggleable=this.options().toggleable||this.multiple,this.onSelects=[],this.onUnselects=[],this.onChanges=[],i?this.select(!0):(this.state=i,this.surgicallyDeselect(!0))}mount(){this.el.hasAttribute(this.options().ariaAttr)||p(this.el,this.options().ariaAttr,"false")}onChange(e){this.onChanges.push(e)}onSelect(e){this.onSelects.push(e)}onUnselect(e){this.onUnselects.push(e)}setState(e){e?this.select():this.deselect()}getState(){return this.state}press(){this.toggleable?this.toggle():this.select()}trigger(){this.toggleable?this.toggle():this.select()}toggle(){this.isSelected()?this.deselect():this.select()}isSelected(){return this.state}select(e=!1){let i=!this.isSelected();this.toggleable||this.group()?.deselectOthers(this.el),this.state=!0,p(this.el,this.options().ariaAttr,"true"),p(this.el,this.options().dataAttr,""),i&&(e||(this.onSelects.forEach(o=>o()),this.onChanges.forEach(o=>o())),this.group()?.changed(this,e))}surgicallySelect(){let e=!this.isSelected();this.state=!0,p(this.el,this.options().ariaAttr,"true"),p(this.el,this.options().dataAttr,""),e&&(this.onSelects.forEach(i=>i()),this.onChanges.forEach(i=>i()))}deselect(e=!0){let i=this.isSelected();this.state=!1,p(this.el,this.options().ariaAttr,"false"),x(this.el,this.options().dataAttr),i&&(this.onUnselects.forEach(o=>o()),this.onChanges.forEach(o=>o()),e&&this.group()?.changed(this))}surgicallyDeselect(e=!1){let i=this.isSelected();this.state=!1,p(this.el,this.options().ariaAttr,"false"),x(this.el,this.options().dataAttr),i&&!e&&(this.onUnselects.forEach(o=>o()),this.onChanges.forEach(o=>o()))}getValue(){return this.value}getLabel(){return this.label}};var qe=class extends ${groupOfType=L;boot({options:e}){e({wrap:!1,ensureTabbable:!0})}mount(){this.options().ensureTabbable&&this.ensureTabbable()}focusFirst(){let e;e=e||this.walker().find(i=>i.hasAttribute("autofocus")),e=e||this.walker().find(i=>i.getAttribute("tabindex")==="0"),e=e||this.walker().find(i=>i.getAttribute("tabindex")==="-1"),e=e||this.walker().find(i=>Ti(i)),e?.focus()}focusPrev(){this.moveFocus(e=>this.options().wrap?this.walker().prevOrLast(e):this.walker().prev(e))}focusNext(){this.moveFocus(e=>this.options().wrap?this.walker().nextOrFirst(e):this.walker().next(e))}focusBySearch(e){let i=this.walker().find(o=>o.textContent.toLowerCase().trim().startsWith(e.toLowerCase()));i?.use(L).tabbable(),i?.use(L).focus()}moveFocus(e){let i=this.walker().find(n=>n.use(L).isTabbable());e(i)?.use(L).focus()}ensureTabbable(){this.walker().findOrFirst(e=>{e.use(L).isTabbable()})?.use(L).tabbable()}wipeTabbables(){this.walker().each(e=>{e.use(L).untabbable()})}untabbleOthers(e){this.walker().each(i=>{i!==e&&i.use(L).untabbable()})}walker(){return V(this.el,(e,{skip:i,reject:o})=>{if(e[this.constructor.name]&&e!==this.el)return o();if(!e[this.groupOfType.name])return i();if(e.hasAttribute("disabled"))return o()})}},L=class extends C{groupedByType=qe;boot({options:e}){e({hover:!1,disableable:null,tabbable:!1,tabbableAttr:null})}mount(){let e=this.options().disableable;if(!e)throw"Focusable requires a Disableable instance...";this.el.hasAttribute("tabindex")||(this.options().tabbable?this.tabbable():this.untabbable()),this.pauseFocusListener=this.on("focus",e.enabled(()=>{this.focus(!1)})).pause,this.on("focus",e.enabled(()=>{Dt()&&p(this.el,"data-focus","")})),this.on("blur",e.enabled(()=>{x(this.el,"data-focus")})),this.options().hover&&this.on("pointerenter",e.enabled(()=>{this.group()?.untabbleOthers(this.el),this.tabbable()})),this.options().hover&&this.on("pointerleave",e.enabled(i=>{this.untabbable()}))}focus(e=!0){this.group()?.untabbleOthers(this.el),this.tabbable(),e&&this.pauseFocusListener(()=>{this.el.focus({focusVisible:!1})})}tabbable(){p(this.el,"tabindex","0"),this.options().tabbableAttr&&p(this.el,this.options().tabbableAttr,"")}untabbable(){p(this.el,"tabindex","-1"),this.options().tabbableAttr&&x(this.el,this.options().tabbableAttr)}isTabbable(){return this.el.getAttribute("tabindex")==="0"}};var ne=class extends C{boot({options:e}){this.onChanges=[],Object.defineProperty(this.el,"disabled",{get:()=>this.el.hasAttribute("disabled"),set:o=>{o?this.el.setAttribute("disabled",""):this.el.removeAttribute("disabled")}}),this.el.hasAttribute("disabled")?this.el.disabled=!0:this.el.closest("[disabled]")&&(this.el.disabled=!0),new MutationObserver(o=>{this.onChanges.forEach(n=>n(this.el.disabled))}).observe(this.el,{attributeFilter:["disabled"]})}onChange(e){this.onChanges.push(e)}onInitAndChange(e){e(this.el.disabled),this.onChanges.push(e)}enabled(e){return(...i)=>{if(!this.el.disabled)return e(...i)}}disabled(e){return(...i)=>{if(this.el.disabled)return e(...i)}}isDisabled(){return this.el.disabled}};var $e=class extends ${groupOfType=O;boot({options:e}){e({wrap:!1,filter:!1}),this.onChanges=[]}onChange(e){this.onChanges.push(e)}activated(e){this.onChanges.forEach(i=>i())}activateFirst(){this.filterAwareWalker().first()?.use(O).activate()}activateBySearch(e){this.filterAwareWalker().find(o=>o.textContent.toLowerCase().trim().startsWith(e.toLowerCase()))?.use(O).activate()}activateSelectedOrFirst(e){if(!e||(o=>o.matches("ui-option")?getComputedStyle(o).display==="none":!1)(e)){this.filterAwareWalker().first()?.use(O).activate();return}e?.use(O).activate()}activateActiveOrFirst(){let e=this.getActive();if(!e){this.filterAwareWalker().first()?.use(O).activate();return}e?.use(O).activate()}activateActiveOrLast(){let e=this.getActive();if(!e){this.filterAwareWalker().last()?.use(O).activate();return}e?.use(O).activate()}activatePrev(){let e=this.getActive();if(!e){this.filterAwareWalker().last()?.use(O).activate();return}let i;this.options.wrap?i=this.filterAwareWalker().prevOrLast(e):i=this.filterAwareWalker().prev(e),i?.use(O).activate()}activateNext(){let e=this.getActive();if(!e){this.filterAwareWalker().first()?.use(O).activate();return}let i;this.options.wrap?i=this.filterAwareWalker().nextOrFirst(e):i=this.filterAwareWalker().next(e),i?.use(O).activate()}getActive(){return this.walker().find(e=>e.use(O).isActive())}clearActive(){this.getActive()?.use(O).deactivate()}filterAwareWalker(){let e=i=>i.matches("ui-option")?getComputedStyle(i).display==="none":!1;return V(this.el,(i,{skip:o,reject:n})=>{if(i[this.constructor.name]&&i!==this.el)return n();if(!i[this.groupOfType.name])return o();if(i.hasAttribute("disabled")||e(i))return n()})}},O=class t extends C{groupedByType=$e;mount(){this.el.addEventListener("mouseenter",()=>{this.activate()}),this.el.addEventListener("mouseleave",()=>{this.deactivate()})}activate(){this.group()&&this.group().walker().each(e=>e.use(t).deactivate(!1)),p(this.el,"data-active",""),this.el.scrollIntoView({block:"nearest"}),this.group()&&this.group().activated(this.el)}deactivate(e=!0){x(this.el,"data-active"),e&&this.group()&&this.group().activated(this.el)}isActive(){return this.el.hasAttribute("data-active")}};var ze=class extends ${groupOfType=ge;boot({options:e}){e({}),this.onChanges=[],this.lastSearch=""}onChange(e){this.onChanges.push(e)}filter(e){e===""?this.walker().each(i=>{i.use(ge).unfilter()}):this.walker().each(i=>{this.matches(i,e)?i.use(ge).unfilter():i.use(ge).filter()}),this.lastSearch!==e&&this.onChanges.forEach(i=>i()),this.lastSearch=e}matches(e,i){return e.textContent.toLowerCase().trim().includes(i.toLowerCase().trim())}hasResults(){return this.walker().some(e=>!e.use(ge).isFiltered())}},ge=class extends C{groupedByType=ze;boot({options:e}){e({mirror:null,keep:!1}),this.onChanges=[]}filter(){this.options().keep||(p(this.el,"data-hidden",""),this.options().mirror&&p(this.options().mirror,"data-hidden",""))}unfilter(){x(this.el,"data-hidden"),this.options().mirror&&x(this.options().mirror,"data-hidden","")}isFiltered(){return this.el.hasAttribute("data-hidden")}};var yt=class extends k{boot(){if(this.querySelectorAll("[data-appended]").forEach(e=>e.remove()),!this.querySelector("template")){let e=document.createElement("template");e.setAttribute("name","placeholder"),e.innerHTML="<span>"+this.innerHTML+"</span>",this.innerHTML="",this.appendChild(e)}if(!this.querySelector('template[name="options"]')){let e=document.createElement("template");e.setAttribute("name","options"),e.innerHTML="<div><slot></slot></div>",this.appendChild(e)}if(!this.querySelector('template[name="option"]')){let e=document.createElement("template");e.setAttribute("name","option"),e.innerHTML="<div><slot></slot></div>",this.appendChild(e)}this.templates={placeholder:this.querySelector('template[name="placeholder"]'),overflow:this.querySelector('template[name="overflow"]'),options:this.querySelector('template[name="options"]'),option:this.querySelector('template[name="option"]')},this.templates.options.elsByValue=new Map,this.max=this.templates.overflow?.getAttribute("max")?this.templates.overflow.getAttribute("max"):1/0,this.selecteds=new Map,this.picker=this.closest("ui-select"),this.multiple=this.picker.hasAttribute("multiple")}mount(){queueMicrotask(()=>{this.picker._selectable.onInitAndChange(()=>{this.render(!0)});let e=this.picker.list();e&&new MutationObserver(i=>{queueMicrotask(()=>this.render())}).observe(e,{childList:!0})})}render(e){if(this.multiple){let i=this.picker.value,o=Array.from(this.selecteds.keys()).filter(r=>!i.includes(r)),n=i.filter(r=>!this.selecteds.has(r));o.forEach(r=>this.selecteds.delete(r));let s=new Map;for(let r of n){let c=this.picker._selectable.findByValue(r);if(!c){if(e)return setTimeout(()=>this.render());throw`Could not find option for value "${r}"`}s.set(r,c)}s.forEach((r,c)=>this.selecteds.set(c,r)),this.templates.placeholder?.clearPlaceholder?.(),this.templates.overflow?.clearOverflow?.(),this.templates.options?.clearOptions?.(),this.selecteds.size>0?this.renderOptions({hasOverflowed:r=>{if(this.max==="auto"){let c=!1;if(this.renderOverflow(this.selecteds.size,this.selecteds.size-r),this.clientWidth<this.scrollWidth&&(c=!0),this.templates.overflow?.clearOverflow?.(),c)return!0}return r>parseInt(this.max)},renderOverflow:r=>{this.templates?.overflow?.getAttribute("mode")!=="append"&&this.templates.options?.clearOptions?.(),this.renderOverflow(this.selecteds.size,r)}}):this.renderPlaceholder()}else{let i=this.picker.value;if(Array.from(this.selecteds.keys()).includes(i))return;this.selecteds.clear();let o=this.picker._selectable.findByValue(i);if(o)this.selecteds.set(i,o);else if(!["",null,void 0].includes(i)){if(e)return setTimeout(()=>{console.log("retrying..."),this.render()});throw`Could not find option for value "${i}"`}this.templates.placeholder?.clearPlaceholder?.(),this.templates.option?.clearOption?.(),this.selecteds.size>0?this.renderOption():this.renderPlaceholder()}}renderOptions({hasOverflowed:e,renderOverflow:i}){let o=document.createElement("div");o.style.display="contents";let n=je(this.templates.options,{default:o});this.templates.options.after(n),this.templates.options.clearOptions=()=>{n.remove(),this.templates.options.clearOptions=()=>{}};let s=0,r=!1;for(let[a,l]of this.selecteds){let h=new DocumentFragment;h.append(...l.el.cloneNode(!0).childNodes);let f=je(this.templates.option,{text:l.el.textContent.trim(),default:h,value:a});if(f.setAttribute("data-value",a),f.setAttribute("data-appended",""),f.deselect=()=>l.deselect(),o.appendChild(f),s++,e(s)){r=!0,o.removeChild(f),s--;break}}let c=new DocumentFragment;c.append(...o.childNodes),o.replaceWith(c),r&&i(this.selecteds.size-s)}renderOption(){for(let[e,i]of this.selecteds){let o=new DocumentFragment;o.append(...i.el.cloneNode(!0).childNodes);let n=je(this.templates.option,{text:i.el.textContent.trim(),default:o,value:e});n.setAttribute("data-value",e),n.setAttribute("data-appended",e),n.deselect=()=>i.deselect(),this.templates.option.after(n),this.templates.option.clearOption=()=>{n.remove(),this.templates.option.clearOption=()=>{}}}}renderPlaceholder(){if(!this.templates.placeholder)return;let e=je(this.templates.placeholder);e.setAttribute("data-appended",""),this.templates.placeholder.after(e),this.templates.placeholder.clearPlaceholder=()=>{e.remove(),this.templates.placeholder.clearPlaceholder=()=>{}}}renderOverflow(e,i){if(!this.templates.overflow)return;let o=je(this.templates.overflow,{remainder:i,count:this.selecteds.size});o.setAttribute("data-appended",""),this.templates.overflow.after(o),this.templates.overflow.clearOverflow=()=>{o.remove(),this.templates.placeholder.clearOverflow=()=>{}}}};function je(t,e={}){let i=t.content.cloneNode(!0);return Object.entries(e).forEach(([o,n])=>{(o==="default"?i.querySelectorAll("slot:not([name])"):i.querySelectorAll(`slot[name="${o}"]`)).forEach(r=>r.replaceWith(typeof n=="string"?document.createTextNode(n):n))}),i.firstElementChild}var Ke=class extends pt{boot(){let e=this.list();this._controllable=new z(this),this._selectable=new oe(e,{multiple:this.hasAttribute("multiple")}),this._controllable.initial(o=>o&&this._selectable.setState(o)),this._controllable.getter(()=>this._selectable.getState());let i=ee();this._controllable.setter(i(o=>{this._selectable.setState(o)})),this._selectable.onChange(i(()=>{this._controllable.dispatch(),this.dispatchEvent(new CustomEvent("select",{bubbles:!1}))}))}mount(){this._disableable=new ne(this);let e=this.input(),i=this.button(),o=this.list(),n=this.hasAttribute("multiple"),s=this.hasAttribute("autocomplete"),r=this.hasAttribute("autocomplete")&&this.getAttribute("autocomplete").trim().split(" ").includes("strict"),c=this.querySelector("ui-options")||this,a=An(c,"options");this._activatable=new $e(c,{filter:"data-hidden"}),!e&&!i&&this._disableable.onInitAndChange(u=>{u?this.removeAttribute("tabindex"):this.setAttribute("tabindex","0")}),this.hasAttribute("filter")&&this.getAttribute("filter")!=="manual"&&(this._filterable=new ze(o),this._filterable.onChange(()=>{this._activatable.clearActive(),this._filterable.hasResults()&&this._activatable.activateFirst()}),this.addEventListener("close",()=>{this._filterable&&this._filterable.filter("")}));let l=this.querySelector("[popover]:not(ui-tooltip > [popover])"),h=l?.querySelector("input"),f=this.querySelector("input");f=l?.contains(f)?null:f;let b=this.querySelector("button");if(b=l?.contains(b)?null:b,!(l||f))Ue(this,this._activatable),Ye(this,this,this._activatable),ao(this,this._activatable,this._selectable);else if(!l&&f){let u=f;this._disableable.onInitAndChange(d=>{d?u&&p(u,"disabled",""):u&&x(u,"disabled")}),Gt(this,u,this._selectable,this._popoverable),ao(u,this._activatable,this._selectable),uo(s,r,this,u,this._selectable,this._popoverable),Xt(u),Kt(u),this._filterable&&Yt(u,this._filterable),co(u,this._activatable),Ue(u,this._activatable),Ye(this,u,this._activatable),At(this,this._activatable)}else if(l&&f){let u=f;p(u,"role","combobox"),p(u,"aria-controls",a);let d=l;this._popoverable=new R(d),this._anchorable=new N(d,{reference:u,matchWidth:!0,position:this.hasAttribute("position")?this.getAttribute("position"):void 0,gap:this.hasAttribute("gap")?this.getAttribute("gap"):void 0,offset:this.hasAttribute("offset")?this.getAttribute("offset"):void 0}),uo(s,r,this,u,this._selectable,this._popoverable),this._disableable.onInitAndChange(v=>{v?u&&p(u,"disabled",""):u&&x(u,"disabled")}),this.querySelectorAll("button").forEach(v=>{d.contains(v)||(p(v,"tabindex","-1"),p(v,"aria-controls",a),p(v,"aria-haspopup","listbox"),xt(v,this._popoverable),w(v,"click",()=>{this._popoverable.toggle(),u.focus()}))}),Gt(this,u,this._selectable,this._popoverable),zt(this,u,d,this._popoverable,this._anchorable),Qt(this,this._popoverable),xt(u,this._popoverable),Xt(u),Kt(u),this._filterable&&Yt(u,this._filterable),co(u,this._activatable),En(u,this._popoverable),Ut(u,this._popoverable,this._activatable,this._selectable),xn(u,this._popoverable),Ue(u,this._activatable),Ye(this,u,this._activatable),At(this,this._activatable),jt(this._popoverable,this._activatable,this._selectable),Jt(this,this._selectable,this._popoverable,n)}else if(l&&h){let u=b,d=h,v=l;p(u,"role","combobox"),p(d,"role","combobox"),p(u,"aria-controls",a),this._disableable.onInitAndChange(g=>{g?(u&&p(u,"disabled",""),d&&p(d,"disabled","")):(u&&x(u,"disabled"),d&&x(d,"disabled"))}),this._popoverable=new R(v),this._anchorable=new N(v,{reference:u,matchWidth:!0,position:this.hasAttribute("position")?this.getAttribute("position"):void 0,gap:this.hasAttribute("gap")?this.getAttribute("gap"):void 0,offset:this.hasAttribute("offset")?this.getAttribute("offset"):void 0}),Xt(d),Kt(d),this._filterable&&Yt(d,this._filterable),Sn(d,this._popoverable),zt(this,u,v,this._popoverable,this._anchorable),Qt(this,this._popoverable),xt(u,this._popoverable),Gt(this,d,this._selectable,this._popoverable),Ut(u,this._popoverable,this._activatable,this._selectable),lo(u,this._popoverable),Ue(d,this._activatable),ro(u,this._activatable,this._popoverable),Ye(this,d,this._activatable),At(this,this._activatable),jt(this._popoverable,this._activatable,this._selectable),Jt(this,this._selectable,this._popoverable,n)}else if(l){let u=b,d=l;p(u,"role","combobox"),p(u,"aria-controls",a),this._disableable.onInitAndChange(v=>{v?(u&&p(u,"disabled",""),e&&p(e,"disabled","")):(u&&x(u,"disabled"),e&&x(e,"disabled"))}),this._popoverable=new R(d),this._anchorable=new N(d,{reference:u,matchWidth:!0,position:this.hasAttribute("position")?this.getAttribute("position"):void 0,gap:this.hasAttribute("gap")?this.getAttribute("gap"):void 0,offset:this.hasAttribute("offset")?this.getAttribute("offset"):void 0}),zt(this,u,d,this._popoverable,this._anchorable),Qt(this,this._popoverable),xt(u,this._popoverable),Ut(u,this._popoverable,this._activatable,this._selectable),lo(u,this._popoverable),Ue(u,this._activatable),ro(u,this._activatable,this._popoverable),Ye(this,u,this._activatable),At(this,this._activatable),jt(this._popoverable,this._activatable,this._selectable),Jt(this,this._selectable,this._popoverable,n)}new MutationObserver(()=>{setTimeout(()=>{if(!this._popoverable||this._popoverable.getState()){let u=this._selectable.selecteds()[0]?.el;setTimeout(()=>{this._activatable.activateSelectedOrFirst(u)})}else this._activatable.clearActive()})}).observe(o,{childList:!0})}button(){return Array.from(this.querySelectorAll("button")).find(e=>e.nextElementSibling?.matches("[popover]"))||null}input(){return this.querySelector("input")}list(){return this.querySelector("ui-options")||this}clear(){this.input()&&(this.input().value="",this.input().dispatchEvent(new Event("input",{bubbles:!1})))}open(){this._popoverable.setState(!0)}close(){this._popoverable.setState(!1)}deselectLast(){!this.hasAttribute("multiple")&&this.value!==null&&(this.value=null,this.dispatchEvent(new Event("input",{bubbles:!1})),this.dispatchEvent(new Event("change",{bubbles:!1}))),this.hasAttribute("multiple")&&this.value.length!==0&&(this.value=this.value.slice(0,-1),this.dispatchEvent(new Event("input",{bubbles:!1})),this.dispatchEvent(new Event("change",{bubbles:!1})))}},Zt=class extends k{boot(){p(this,"data-hidden","")}mount(){queueMicrotask(()=>{let e=this.closest("ui-autocomplete, ui-combobox, ui-select"),i=this.closest("ui-options");if(!i)return;let o=c=>c.hasAttribute("data-hidden"),n=()=>{let c;CSS.supports("selector(&)")?c=Array.from(i.querySelectorAll("& > ui-option")).filter(a=>!o(a)).length===0:c=Array.from(i.querySelectorAll(":scope > ui-option")).filter(a=>!o(a)).length===0,c?x(this,"data-hidden"):p(this,"data-hidden","")};n();let s=e._filterable;s&&s.onChange(n),new MutationObserver(c=>{setTimeout(()=>n())}).observe(i,{childList:!0})})}};T("selected",yt);T("select",Ke);T("empty",Zt);Z(({css:t})=>t`ui-select { display: block; }`);Z(({css:t})=>t`ui-selected-option { display: contents; }`);Z(({css:t})=>t`ui-empty { display: block; cursor: default; }`);function Ue(t,e){w(t,"keydown",i=>{["ArrowDown","ArrowUp","Escape"].includes(i.key)&&(i.key==="ArrowDown"?(e.activateNext(),i.preventDefault(),i.stopPropagation()):i.key==="ArrowUp"&&(e.activatePrev(),i.preventDefault(),i.stopPropagation()))})}function ro(t,e,i){ft(t,o=>{e.activateBySearch(o),i.getState()||e.getActive()?.click()})}function Ye(t,e,i){w(e,"keydown",o=>{if(o.key==="Enter"){let n=i.getActive();if(o.preventDefault(),o.stopPropagation(),!n)return;n.click(),t.dispatchEvent(new CustomEvent("action",{bubbles:!1,cancelable:!1}))}})}function At(t,e,i=!1){w(t,i?"pointerdown":"click",o=>{if(o.target.closest("ui-option")){let n=o.target.closest("ui-option");if(n._disabled)return;n._selectable?.trigger(),t.dispatchEvent(new CustomEvent("action",{bubbles:!1,cancelable:!1})),o.preventDefault(),o.stopPropagation()}})}function ao(t,e,i){w(t,"focus",()=>{let o=i.selecteds()[0]?.el;e.activateSelectedOrFirst(o)}),w(t,"blur",()=>{e.clearActive()})}function An(t){let e=q(t,"options");return p(t,"role","listbox"),e}function xt(t,e){p(t,"aria-haspopup","listbox");let i=()=>{p(t,"aria-expanded",e.getState()?"true":"false"),e.getState()?p(t,"data-open",""):x(t,"data-open","")};e.onChange(()=>{i()}),i()}function zt(t,e,i,o,n){let s=()=>{Array.from([t,i]).forEach(r=>{o.getState()?p(r,"data-open",""):x(r,"data-open","")}),o.getState()?n.reposition():n.cleanup()};o.onChange(()=>s()),s(),o.onChange(()=>{o.getState()?t.dispatchEvent(new Event("open",{bubbles:!1,cancelable:!1})):t.dispatchEvent(new Event("close",{bubbles:!1,cancelable:!1}))})}function jt(t,e,i){t.onChange(()=>{if(t.getState()){let o=i.selecteds()[0]?.el;setTimeout(()=>{e.activateSelectedOrFirst(o)})}else e.clearActive()})}function Ut(t,e){w(t,"keydown",i=>{["ArrowDown","ArrowUp","Escape"].includes(i.key)&&(i.key==="ArrowDown"||i.key==="ArrowUp"?e.getState()||(e.setState(!0),i.preventDefault(),i.stopImmediatePropagation()):i.key==="Escape"&&e.getState()&&(e.setState(!1),i.preventDefault(),i.stopImmediatePropagation()))})}function xn(t,e){w(t,"click",()=>{e.getState()||(e.setState(!0),t.focus())})}function lo(t,e){w(t,"click",()=>{e.setState(!e.getState()),t.focus()})}function Sn(t,e){e.onChange(()=>{e.getState()&&setTimeout(()=>t.focus())})}function Yt(t,e){e&&w(t,"input",i=>{e.filter(i.target.value)})}function Kt(t){w(t,"focus",()=>t.select())}function Xt(t){w(t,"change",e=>e.stopPropagation()),w(t,"input",e=>e.stopPropagation())}function En(t,e){w(t,"keydown",i=>{(/^[a-zA-Z0-9]$/.test(i.key)||i.key==="Backspace")&&(e.getState()||e.setState(!0))})}function Gt(t,e,i,o){if(!t.hasAttribute("clear"))return;let s=f=>{e.value=f,e.dispatchEvent(new Event("input",{bubbles:!1}))},r=t.getAttribute("clear"),c=r===""||r.split(" ").includes("action"),a=r===""||r.split(" ").includes("select"),l=r===""||r.split(" ").includes("close"),h=r===""||r.split(" ").includes("esc");r==="none"&&(c=a=l=h=!1),c?t.addEventListener("action",f=>{s("")}):a&&i.onChange(()=>{queueMicrotask(()=>s(""))}),l&&o.onChange(()=>{o.getState()||s("")}),h&&w(e,"keydown",f=>{f.key==="Escape"&&s("")})}function Jt(t,e,i,o){let n=!o,s=!o;if(t.hasAttribute("close")){let r=t.getAttribute("close");n=r===""||r.split(" ").includes("action"),s=r.split(" ").includes("select"),r==="none"&&(n=s=!1)}n?t.addEventListener("action",r=>{i.setState(!1)}):s&&e.onChange(()=>{i.setState(!1)})}function co(t,e){e.onChange(()=>{let i=e.getActive();i?p(t,"aria-activedescendant",i.id):x(t,"aria-activedescendant")})}function uo(t,e,i,o,n,s){if(!t){p(o,"autocomplete","off"),p(o,"aria-autocomplete","none");return}let r=c=>{o.value=c,o.dispatchEvent(new Event("input",{bubbles:!1}))};p(o,"autocomplete","off"),p(o,"aria-autocomplete","list"),n.setState(o.value),queueMicrotask(()=>{n.onInitAndChange(()=>{o.value=n.selectedTextValue()})}),i.addEventListener("action",c=>{r(n.selectedTextValue())}),e&&s.onChange(()=>{s.getState()||r(n.selectedTextValue())})}function Qt(t,e){let{lock:i,unlock:o}=ht();e.onChange(()=>{e.getState()?i():o()})}var ei=class t extends k{boot(){if(this._focusable=new qe(this,{wrap:!1,ensureTabbable:!1}),w(this,"keydown",e=>{["ArrowDown"].includes(e.key)?(e.target===this?this._focusable.focusFirst():this._focusable.focusNext(),e.preventDefault(),e.stopPropagation()):["ArrowUp"].includes(e.key)&&(e.target===this?this._focusable.focusFirst():this._focusable.focusPrev(),e.preventDefault(),e.stopPropagation())}),ft(this,e=>this._focusable.focusBySearch(e)),this.hasAttribute("popover")&&this.addEventListener("lofi-close-popovers",()=>{setTimeout(()=>this.hidePopover(),50)}),this.parentElement.localName==="ui-dropdown"){let e=this.parentElement;w(e.trigger(),"keydown",i=>{i.key==="ArrowDown"&&(this.fromArrowDown=!0,this.showPopover(),i.preventDefault(),i.stopPropagation())})}p(this,"role","menu"),p(this,"tabindex","-1")}mount(){this.initializeMenuItems(),new MutationObserver(i=>{this.initializeMenuItems()}).observe(this,{childList:!0,subtree:!0})}onPopoverShow(){queueMicrotask(()=>{this.fromArrowDown?(this._focusable.focusFirst(),this.fromArrowDown=!1):this.focus()})}onPopoverHide(){this._focusable.wipeTabbables()}initializeMenuItems(){this.walker().each(e=>{e._disableable||kn(e)})}walker(){return V(this,(e,{skip:i,reject:o})=>{if(e instanceof t||e instanceof Ke)return o();if(!["a","button"].includes(e.localName))return i()})}},ti=class extends k{boot(){}},ii=class extends k{boot(){this._disabled=this.hasAttribute("disabled"),this._disableable=new ne(this);let e=this;if(this._disabled&&(p(e,"disabled",""),p(e,"aria-disabled","true")),q(e,"menu-checkbox"),p(e,"role","menuitemcheckbox"),this._disabled)return;e._focusable=new L(e,{disableable:this._disableable,hover:!0,tabbableAttr:"data-active"}),e._selectable=new _(e,{toggleable:!0,value:this.hasAttribute("value")?this.getAttribute("value"):e.textContent.trim(),label:this.hasAttribute("label")?this.getAttribute("label"):e.textContent.trim(),dataAttr:"data-checked",ariaAttr:"aria-checked",selectedInitially:this.hasAttribute("checked")}),this._controllable=new z(this),this._controllable.initial(o=>o&&e._selectable.setState(o)),this._controllable.getter(()=>e._selectable.getState());let i=ee();this._controllable.setter(i(o=>{this._selectable.setState(o)})),this._selectable.onChange(i(()=>{this._controllable.dispatch()})),w(e,"click",()=>{this.dispatchEvent(new CustomEvent("lofi-close-popovers",{bubbles:!0})),e._selectable.press()}),ri(e)}},oi=class extends k{boot(){this._disabled=this.hasAttribute("disabled"),this._disableable=new ne(this);let e=this;this._disabled&&(p(e,"disabled",""),p(e,"aria-disabled","true")),q(e,"menu-radio"),p(e,"role","menuitemradio"),!this._disabled&&(e._focusable=new L(e,{disableable:this._disableable,hover:!0,tabbableAttr:"data-active"}),e._selectable=new _(e,{toggleable:!1,value:this.hasAttribute("value")?this.getAttribute("value"):e.textContent.trim(),label:this.hasAttribute("label")?this.getAttribute("label"):e.textContent.trim(),dataAttr:"data-checked",ariaAttr:"aria-checked",selectedInitially:this.hasAttribute("checked")}),w(e,"click",()=>{this.dispatchEvent(new CustomEvent("lofi-close-popovers",{bubbles:!0})),e._selectable.press()}),ri(e))}},ni=class extends k{boot(){this._selectable=new oe(this),this._controllable=new z(this),p(this,"role","group"),this._controllable.initial(i=>i&&this._selectable.setState(i)),this._controllable.getter(()=>this._selectable.getState());let e=ee();this._controllable.setter(e(i=>{this._selectable.setState(i)})),this._selectable.onChange(e(()=>{this._controllable.dispatch()}))}},si=class extends k{boot(){this._selectable=new oe(this,{multiple:!0}),this._controllable=new z(this),p(this,"role","group"),this._controllable.initial(i=>i&&this._selectable.setState(i)),this._controllable.getter(()=>this._selectable.getState());let e=ee();this._controllable.setter(e(i=>{this._selectable.setState(i)})),this._selectable.onChange(e(()=>{this._controllable.dispatch()}))}};Z(({css:t})=>t`ui-menu[popover]:popover-open { display: block; }`);Z(({css:t})=>t`ui-menu[popover].\:popover-open { display: block; }`);Z(({css:t})=>t`ui-menu-checkbox, ui-menu-radio { cursor: default; display: contents; }`);T("menu",ei);T("submenu",ti);T("menu-checkbox",ii);T("menu-radio",oi);T("menu-radio-group",ni);T("menu-checkbox-group",si);function ri(t){w(t,"keydown",e=>{e.key==="Enter"&&(t.click(),e.preventDefault(),e.stopPropagation())}),w(t,"keydown",e=>{e.key===" "&&(e.preventDefault(),e.stopPropagation())}),w(t,"keyup",e=>{e.key===" "&&(t.click(),e.preventDefault(),e.stopPropagation())})}function kn(t){t._disableable=new ne(t),t._disabled=t.hasAttribute("disabled");let e=t.querySelector("a"),i=t,o=t.parentElement.matches("ui-submenu")&&t.parentElement.querySelector("ui-menu[popover]"),n=e||i;if(t._disabled&&(p(n,"disabled",""),p(n,"aria-disabled","true")),q(n,"menu-item"),p(n,"role","menuitem"),!t._disabled)if(n._focusable=new L(n,{disableable:t._disableable,hover:!0,tabbableAttr:"data-active"}),!o)t.hasAttribute("disabled")||w(t,"click",()=>{t.dispatchEvent(new CustomEvent("lofi-close-popovers",{bubbles:!0}))}),ri(i);else{o._popoverable=new R(o,{triggers:[i]}),o._anchorable=new N(o,{reference:i,position:o.hasAttribute("position")?o.getAttribute("position"):"right start",gap:o.hasAttribute("gap")?o.getAttribute("gap"):"-5",crossAxis:!0}),i.addEventListener("click",r=>{o._popoverable.setState(!0)});let{clear:s}=Se(i,o,{gain(){o._popoverable.setState(!0)},lose(){o._popoverable.setState(!1)},focusable:!1});o._popoverable.onChange(()=>{o._popoverable.getState()||(s(),o._focusable.wipeTabbables()),o._popoverable.getState()?o._anchorable.reposition():o._anchorable.cleanup()}),w(i,"keydown",r=>{r.key==="Enter"&&(o._popoverable.setState(!0),setTimeout(()=>o._focusable.focusFirst()))}),w(i,"keydown",r=>{r.key==="ArrowRight"&&(o._popoverable.setState(!0),setTimeout(()=>o._focusable.focusFirst()))}),w(o,"keydown",r=>{r.key==="ArrowLeft"&&(o._popoverable.setState(!1),i.focus(),r.stopPropagation())})}}document.addEventListener("alpine:init",()=>{let t=window.Flux?.applyAppearance;t||(t=()=>{window.Flux.appearance=null,window.localStorage.removeItem("flux.appearance")});let e=Alpine.reactive({toast(...o){let n={slots:{},dataset:{}};typeof o[0]=="string"&&(n.slots.text=o.shift()),typeof o[0]=="string"&&(n.slots.heading=n.slots.text,n.slots.text=o.shift());let s=o.shift()||{};s.text&&(n.slots.text=s.text),s.heading&&(n.slots.heading=s.heading),s.variant&&(n.dataset.variant=s.variant),s.position&&(n.dataset.position=s.position),s.duration!==void 0&&(n.duration=s.duration),document.dispatchEvent(new CustomEvent("toast-show",{detail:n}))},modal(o){return{show(){document.dispatchEvent(new CustomEvent("modal-show",{detail:{name:o}}))},close(){document.dispatchEvent(new CustomEvent("modal-close",{detail:{name:o}}))}}},modals(){return{close(){document.dispatchEvent(new CustomEvent("modal-close",{detail:{}}))}}},appearance:window.localStorage.getItem("flux.appearance")||"system",systemAppearanceChanged:1,get dark(){return JSON.stringify(e.systemAppearanceChanged),e.appearance==="system"?window.matchMedia("(prefers-color-scheme: dark)").matches:e.appearance==="dark"},set dark(o){let n=this.dark;o!==n&&(o?e.appearance="dark":e.appearance="light")}});window.Flux=e,Alpine.magic("flux",()=>e),Alpine.effect(()=>{t(e.appearance)}),document.addEventListener("livewire:navigated",()=>{t(e.appearance)}),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",()=>{e.systemAppearanceChanged++,t(e.appearance)})});!Ei()&&!ki()&&_i();})();
