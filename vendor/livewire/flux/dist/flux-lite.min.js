(()=>{var at=class extends Event{oldState;newState;constructor(t,{oldState:e="",newState:i="",...s}={}){super(t,s),this.oldState=String(e||""),this.newState=String(i||"")}},Ti=new WeakMap;function Ri(t,e,i){Ti.set(t,setTimeout(()=>{Ti.has(t)&&t.dispatchEvent(new at("toggle",{cancelable:!1,oldState:e,newState:i}))},0))}var Ft=globalThis.ShadowRoot||function(){},Ds=globalThis.HTMLDialogElement||function(){},st=new WeakMap,J=new WeakMap,Be=new WeakMap;function ot(t){return Be.get(t)||"hidden"}var nt=new WeakMap;function Rs(t){let e=t.popoverTargetElement;if(!(e instanceof HTMLElement))return;let i=ot(e);t.popoverTargetAction==="show"&&i==="showing"||t.popoverTargetAction==="hide"&&i==="hidden"||(i==="showing"?He(e,!0,!0):de(e,!1)&&(nt.set(e,t),Rt(e)))}function de(t,e){return!(t.popover!=="auto"&&t.popover!=="manual"||!t.isConnected||e&&ot(t)!=="showing"||!e&&ot(t)!=="hidden"||t instanceof Ds&&t.hasAttribute("open")||document.fullscreenElement===t)}function Pi(t){return t?Array.from(J.get(t.ownerDocument)||[]).indexOf(t)+1:0}function Fs(t){let e=Fi(t),i=Is(t);return Pi(e)>Pi(i)?e:i}function rt(t){let e=J.get(t);for(let i of e||[])if(!i.isConnected)e.delete(i);else return i;return null}function Se(t){return typeof t.getRootNode=="function"?t.getRootNode():t.parentNode?Se(t.parentNode):t}function Fi(t){for(;t;){if(t instanceof HTMLElement&&t.popover==="auto"&&Be.get(t)==="showing")return t;if(t=t instanceof Element&&t.assignedSlot||t.parentElement||Se(t),t instanceof Ft&&(t=t.host),t instanceof Document)return}}function Is(t){for(;t;){let e=t.popoverTargetElement;if(e instanceof HTMLElement)return e;if(t=t.parentElement||Se(t),t instanceof Ft&&(t=t.host),t instanceof Document)return}}function Ns(t){let e=new Map,i=0;for(let n of J.get(t.ownerDocument)||[])e.set(n,i),i+=1;e.set(t,i),i+=1;let s=null;function o(n){let r=Fi(n);if(r===null)return null;let c=e.get(r);(s===null||e.get(s)<c)&&(s=r)}return o(t.parentElement||Se(t)),s}function Ws(t){return t.hidden||t instanceof Ft||(t instanceof HTMLButtonElement||t instanceof HTMLInputElement||t instanceof HTMLSelectElement||t instanceof HTMLTextAreaElement||t instanceof HTMLOptGroupElement||t instanceof HTMLOptionElement||t instanceof HTMLFieldSetElement)&&t.disabled||t instanceof HTMLInputElement&&t.type==="hidden"||t instanceof HTMLAnchorElement&&t.href===""?!1:typeof t.tabIndex=="number"&&t.tabIndex!==-1}function Hs(t){if(t.shadowRoot&&t.shadowRoot.delegatesFocus!==!0)return null;let e=t;e.shadowRoot&&(e=e.shadowRoot);let i=e.querySelector("[autofocus]");if(i)return i;{let n=e.querySelectorAll("slot");for(let r of n){let c=r.assignedElements({flatten:!0});for(let l of c){if(l.hasAttribute("autofocus"))return l;if(i=l.querySelector("[autofocus]"),i)return i}}}let s=t.ownerDocument.createTreeWalker(e,NodeFilter.SHOW_ELEMENT),o=s.currentNode;for(;o;){if(Ws(o))return o;o=s.nextNode()}}function Bs(t){Hs(t)?.focus()}var lt=new WeakMap;function Rt(t){if(!de(t,!1))return;let e=t.ownerDocument;if(!t.dispatchEvent(new at("beforetoggle",{cancelable:!0,oldState:"closed",newState:"open"}))||!de(t,!1))return;let i=!1;if(t.popover==="auto"){let o=t.getAttribute("popover"),n=Ns(t)||e;if(ct(n,!1,!0),o!==t.getAttribute("popover")||!de(t,!1))return}rt(e)||(i=!0),lt.delete(t);let s=e.activeElement;t.classList.add(":popover-open"),Be.set(t,"showing"),st.has(e)||st.set(e,new Set),st.get(e).add(t),Bs(t),t.popover==="auto"&&(J.has(e)||J.set(e,new Set),J.get(e).add(t),Ii(nt.get(t),!0)),i&&s&&t.popover==="auto"&&lt.set(t,s),Ri(t,"closed","open")}function He(t,e=!1,i=!1){if(!de(t,!0))return;let s=t.ownerDocument;if(t.popover==="auto"&&(ct(t,e,i),!de(t,!0))||(Ii(nt.get(t),!1),nt.delete(t),i&&(t.dispatchEvent(new at("beforetoggle",{oldState:"open",newState:"closed"})),!de(t,!0))))return;st.get(s)?.delete(t),J.get(s)?.delete(t),t.classList.remove(":popover-open"),Be.set(t,"hidden"),i&&Ri(t,"open","closed");let o=lt.get(t);o&&(lt.delete(t),e&&o.focus())}function Li(t,e=!1,i=!1){let s=rt(t);for(;s;)He(s,e,i),s=rt(t)}function ct(t,e,i){let s=t.ownerDocument||t;if(t instanceof Document)return Li(s,e,i);let o=null,n=!1;for(let r of J.get(s)||[])if(r===t)n=!0;else if(n){o=r;break}if(!n)return Li(s,e,i);for(;o&&ot(o)==="showing"&&J.get(s)?.size;)He(o,e,i)}var Lt=new WeakMap;function Mi(t){if(!t.isTrusted)return;let e=t.composedPath()[0];if(!e)return;let i=e.ownerDocument;if(!rt(i))return;let o=Fs(e);if(o&&t.type==="pointerdown")Lt.set(i,o);else if(t.type==="pointerup"){let n=Lt.get(i)===o;Lt.delete(i),n&&ct(o||i,!1,!0)}}var Mt=new WeakMap;function Ii(t,e=!1){if(!t)return;Mt.has(t)||Mt.set(t,t.getAttribute("aria-expanded"));let i=t.popoverTargetElement;if(i instanceof HTMLElement&&i.popover==="auto")t.setAttribute("aria-expanded",String(e));else{let s=Mt.get(t);s?t.setAttribute("aria-expanded",s):t.removeAttribute("aria-expanded")}}var Di=globalThis.ShadowRoot||function(){};function Vs(){return typeof HTMLElement<"u"&&typeof HTMLElement.prototype=="object"&&"popover"in HTMLElement.prototype}function he(t,e,i){let s=t[e];Object.defineProperty(t,e,{value(o){return s.call(this,i(o))}})}var qs=/(^|[^\\]):popover-open\b/g;function $s(){return typeof globalThis.CSSLayerBlockRule=="function"}function zs(){let t=$s();return`
${t?"@layer popover-polyfill {":""}
  :where([popover]) {
    position: fixed;
    z-index: 2147483647;
    inset: 0;
    padding: 0.25em;
    width: fit-content;
    height: fit-content;
    border-width: initial;
    border-color: initial;
    border-image: initial;
    border-style: solid;
    background-color: canvas;
    color: canvastext;
    overflow: auto;
    margin: auto;
  }

  :where([popover]:not(.\\:popover-open)) {
    display: none;
  }

  :where(dialog[popover].\\:popover-open) {
    display: block;
  }

  :where(dialog[popover][open]) {
    display: revert;
  }

  :where([anchor].\\:popover-open) {
    inset: auto;
  }

  :where([anchor]:popover-open) {
    inset: auto;
  }

  @supports not (background-color: canvas) {
    :where([popover]) {
      background-color: white;
      color: black;
    }
  }

  @supports (width: -moz-fit-content) {
    :where([popover]) {
      width: -moz-fit-content;
      height: -moz-fit-content;
    }
  }

  @supports not (inset: 0) {
    :where([popover]) {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }
${t?"}":""}
`}var _e=null;function Dt(t){let e=zs();if(_e===null)try{_e=new CSSStyleSheet,_e.replaceSync(e)}catch{_e=!1}if(_e===!1){let i=document.createElement("style");i.textContent=e,t instanceof Document?t.head.prepend(i):t.prepend(i)}else t.adoptedStyleSheets=[_e,...t.adoptedStyleSheets]}function js(){if(typeof window>"u")return;window.ToggleEvent=window.ToggleEvent||at;function t(l){return l?.includes(":popover-open")&&(l=l.replace(qs,"$1.\\:popover-open")),l}he(Document.prototype,"querySelector",t),he(Document.prototype,"querySelectorAll",t),he(Element.prototype,"querySelector",t),he(Element.prototype,"querySelectorAll",t),he(Element.prototype,"matches",t),he(Element.prototype,"closest",t),he(DocumentFragment.prototype,"querySelectorAll",t),Object.defineProperties(HTMLElement.prototype,{popover:{enumerable:!0,configurable:!0,get(){if(!this.hasAttribute("popover"))return null;let l=(this.getAttribute("popover")||"").toLowerCase();return l===""||l=="auto"?"auto":"manual"},set(l){l===null?this.removeAttribute("popover"):this.setAttribute("popover",l)}},showPopover:{enumerable:!0,configurable:!0,value(){Rt(this)}},hidePopover:{enumerable:!0,configurable:!0,value(){He(this,!0,!0)}},togglePopover:{enumerable:!0,configurable:!0,value(l){Be.get(this)==="showing"&&l===void 0||l===!1?He(this,!0,!0):(l===void 0||l===!0)&&Rt(this)}}});let e=Element.prototype.attachShadow;e&&Object.defineProperties(Element.prototype,{attachShadow:{enumerable:!0,configurable:!0,writable:!0,value(l){let a=e.call(this,l);return Dt(a),a}}});let i=HTMLElement.prototype.attachInternals;i&&Object.defineProperties(HTMLElement.prototype,{attachInternals:{enumerable:!0,configurable:!0,writable:!0,value(){let l=i.call(this);return l.shadowRoot&&Dt(l.shadowRoot),l}}});let s=new WeakMap;function o(l){Object.defineProperties(l.prototype,{popoverTargetElement:{enumerable:!0,configurable:!0,set(a){if(a===null)this.removeAttribute("popovertarget"),s.delete(this);else if(a instanceof Element)this.setAttribute("popovertarget",""),s.set(this,a);else throw new TypeError("popoverTargetElement must be an element or null")},get(){if(this.localName!=="button"&&this.localName!=="input"||this.localName==="input"&&this.type!=="reset"&&this.type!=="image"&&this.type!=="button"||this.disabled||this.form&&this.type==="submit")return null;let a=s.get(this);if(a&&a.isConnected)return a;if(a&&!a.isConnected)return s.delete(this),null;let f=Se(this),d=this.getAttribute("popovertarget");return(f instanceof Document||f instanceof Di)&&d&&f.getElementById(d)||null}},popoverTargetAction:{enumerable:!0,configurable:!0,get(){let a=(this.getAttribute("popovertargetaction")||"").toLowerCase();return a==="show"||a==="hide"?a:"toggle"},set(a){this.setAttribute("popovertargetaction",a)}}})}o(HTMLButtonElement),o(HTMLInputElement);let n=l=>{let a=l.composedPath(),f=a[0];if(!(f instanceof Element)||f?.shadowRoot)return;let d=Se(f);if(!(d instanceof Di||d instanceof Document))return;let m=a.find(g=>g.matches?.("[popovertargetaction],[popovertarget]"));if(m){Rs(m),l.preventDefault();return}},r=l=>{let a=l.key,f=l.target;!l.defaultPrevented&&f&&(a==="Escape"||a==="Esc")&&ct(f.ownerDocument,!0,!0)};(l=>{l.addEventListener("click",n),l.addEventListener("keydown",r),l.addEventListener("pointerdown",Mi),l.addEventListener("pointerup",Mi)})(document),Dt(document)}Vs()||js();var bt=class extends Event{oldState;newState;constructor(t,{oldState:e="",newState:i="",...s}={}){super(t,s),this.oldState=String(e||""),this.newState=String(i||"")}},Ni=new WeakMap;function qi(t,e,i){Ni.set(t,setTimeout(()=>{Ni.has(t)&&t.dispatchEvent(new bt("toggle",{cancelable:!1,oldState:e,newState:i}))},0))}var Bt=globalThis.ShadowRoot||function(){},Us=globalThis.HTMLDialogElement||function(){},ut=new WeakMap,Q=new WeakMap,qe=new WeakMap;function ht(t){return qe.get(t)||"hidden"}var dt=new WeakMap;function Ys(t){let e=t.popoverTargetElement;if(!(e instanceof HTMLElement))return;let i=ht(e);t.popoverTargetAction==="show"&&i==="showing"||t.popoverTargetAction==="hide"&&i==="hidden"||(i==="showing"?Ve(e,!0,!0):pe(e,!1)&&(dt.set(e,t),Ht(e)))}function pe(t,e){return!(t.popover!=="auto"&&t.popover!=="manual"||!t.isConnected||e&&ht(t)!=="showing"||!e&&ht(t)!=="hidden"||t instanceof Us&&t.hasAttribute("open")||document.fullscreenElement===t)}function Wi(t){return t?Array.from(Q.get(t.ownerDocument)||[]).indexOf(t)+1:0}function Ks(t){let e=$i(t),i=Xs(t);return Wi(e)>Wi(i)?e:i}function ft(t){let e=Q.get(t);for(let i of e||[])if(!i.isConnected)e.delete(i);else return i;return null}function Ee(t){return typeof t.getRootNode=="function"?t.getRootNode():t.parentNode?Ee(t.parentNode):t}function $i(t){for(;t;){if(t instanceof HTMLElement&&t.popover==="auto"&&qe.get(t)==="showing")return t;if(t=t instanceof Element&&t.assignedSlot||t.parentElement||Ee(t),t instanceof Bt&&(t=t.host),t instanceof Document)return}}function Xs(t){for(;t;){let e=t.popoverTargetElement;if(e instanceof HTMLElement)return e;if(t=t.parentElement||Ee(t),t instanceof Bt&&(t=t.host),t instanceof Document)return}}function Gs(t){let e=new Map,i=0;for(let n of Q.get(t.ownerDocument)||[])e.set(n,i),i+=1;e.set(t,i),i+=1;let s=null;function o(n){let r=$i(n);if(r===null)return null;let c=e.get(r);(s===null||e.get(s)<c)&&(s=r)}return o(t.parentElement||Ee(t)),s}function Js(t){return t.hidden||t instanceof Bt||(t instanceof HTMLButtonElement||t instanceof HTMLInputElement||t instanceof HTMLSelectElement||t instanceof HTMLTextAreaElement||t instanceof HTMLOptGroupElement||t instanceof HTMLOptionElement||t instanceof HTMLFieldSetElement)&&t.disabled||t instanceof HTMLInputElement&&t.type==="hidden"||t instanceof HTMLAnchorElement&&t.href===""?!1:typeof t.tabIndex=="number"&&t.tabIndex!==-1}function Qs(t){if(t.shadowRoot&&t.shadowRoot.delegatesFocus!==!0)return null;let e=t;e.shadowRoot&&(e=e.shadowRoot);let i=e.querySelector("[autofocus]");if(i)return i;{let n=e.querySelectorAll("slot");for(let r of n){let c=r.assignedElements({flatten:!0});for(let l of c){if(l.hasAttribute("autofocus"))return l;if(i=l.querySelector("[autofocus]"),i)return i}}}let s=t.ownerDocument.createTreeWalker(e,NodeFilter.SHOW_ELEMENT),o=s.currentNode;for(;o;){if(Js(o))return o;o=s.nextNode()}}function Zs(t){Qs(t)?.focus()}var pt=new WeakMap;function Ht(t){if(!pe(t,!1))return;let e=t.ownerDocument;if(!t.dispatchEvent(new bt("beforetoggle",{cancelable:!0,oldState:"closed",newState:"open"}))||!pe(t,!1))return;let i=!1;if(t.popover==="auto"){let o=t.getAttribute("popover"),n=Gs(t)||e;if(gt(n,!1,!0),o!==t.getAttribute("popover")||!pe(t,!1))return}ft(e)||(i=!0),pt.delete(t);let s=e.activeElement;t.classList.add(":popover-open"),qe.set(t,"showing"),ut.has(e)||ut.set(e,new Set),ut.get(e).add(t),Zs(t),t.popover==="auto"&&(Q.has(e)||Q.set(e,new Set),Q.get(e).add(t),zi(dt.get(t),!0)),i&&s&&t.popover==="auto"&&pt.set(t,s),qi(t,"closed","open")}function Ve(t,e=!1,i=!1){if(!pe(t,!0))return;let s=t.ownerDocument;if(t.popover==="auto"&&(gt(t,e,i),!pe(t,!0))||(zi(dt.get(t),!1),dt.delete(t),i&&(t.dispatchEvent(new bt("beforetoggle",{oldState:"open",newState:"closed"})),!pe(t,!0))))return;ut.get(s)?.delete(t),Q.get(s)?.delete(t),t.classList.remove(":popover-open"),qe.set(t,"hidden"),i&&qi(t,"open","closed");let o=pt.get(t);o&&(pt.delete(t),e&&o.focus())}function Hi(t,e=!1,i=!1){let s=ft(t);for(;s;)Ve(s,e,i),s=ft(t)}function gt(t,e,i){let s=t.ownerDocument||t;if(t instanceof Document)return Hi(s,e,i);let o=null,n=!1;for(let r of Q.get(s)||[])if(r===t)n=!0;else if(n){o=r;break}if(!n)return Hi(s,e,i);for(;o&&ht(o)==="showing"&&Q.get(s)?.size;)Ve(o,e,i)}var It=new WeakMap;function Bi(t){if(!t.isTrusted)return;let e=t.composedPath()[0];if(!e)return;let i=e.ownerDocument;if(!ft(i))return;let o=Ks(e);if(o&&t.type==="pointerdown")It.set(i,o);else if(t.type==="pointerup"){let n=It.get(i)===o;It.delete(i),n&&gt(o||i,!1,!0)}}var Nt=new WeakMap;function zi(t,e=!1){if(!t)return;Nt.has(t)||Nt.set(t,t.getAttribute("aria-expanded"));let i=t.popoverTargetElement;if(i instanceof HTMLElement&&i.popover==="auto")t.setAttribute("aria-expanded",String(e));else{let s=Nt.get(t);s?t.setAttribute("aria-expanded",s):t.removeAttribute("aria-expanded")}}var Vi=globalThis.ShadowRoot||function(){};function ji(){return typeof HTMLElement<"u"&&typeof HTMLElement.prototype=="object"&&"popover"in HTMLElement.prototype}function Ui(){return!!(document.body?.showPopover&&!/native code/i.test(document.body.showPopover.toString()))}function fe(t,e,i){let s=t[e];Object.defineProperty(t,e,{value(o){return s.call(this,i(o))}})}var eo=/(^|[^\\]):popover-open\b/g;function to(){return typeof globalThis.CSSLayerBlockRule=="function"}function io(){let t=to();return`
${t?"@layer popover-polyfill {":""}
  :where([popover]) {
    position: fixed;
    z-index: 2147483647;
    inset: 0;
    padding: 0.25em;
    width: fit-content;
    height: fit-content;
    border-width: initial;
    border-color: initial;
    border-image: initial;
    border-style: solid;
    background-color: canvas;
    color: canvastext;
    overflow: auto;
    margin: auto;
  }

  :where([popover]:not(.\\:popover-open)) {
    display: none;
  }

  :where(dialog[popover].\\:popover-open) {
    display: block;
  }

  :where(dialog[popover][open]) {
    display: revert;
  }

  :where([anchor].\\:popover-open) {
    inset: auto;
  }

  :where([anchor]:popover-open) {
    inset: auto;
  }

  @supports not (background-color: canvas) {
    :where([popover]) {
      background-color: white;
      color: black;
    }
  }

  @supports (width: -moz-fit-content) {
    :where([popover]) {
      width: -moz-fit-content;
      height: -moz-fit-content;
    }
  }

  @supports not (inset: 0) {
    :where([popover]) {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }
${t?"}":""}
`}var ke=null;function Wt(t){let e=io();if(ke===null)try{ke=new CSSStyleSheet,ke.replaceSync(e)}catch{ke=!1}if(ke===!1){let i=document.createElement("style");i.textContent=e,t instanceof Document?t.head.prepend(i):t.prepend(i)}else t.adoptedStyleSheets=[ke,...t.adoptedStyleSheets]}function Yi(){if(typeof window>"u")return;window.ToggleEvent=window.ToggleEvent||bt;function t(l){return l?.includes(":popover-open")&&(l=l.replace(eo,"$1.\\:popover-open")),l}fe(Document.prototype,"querySelector",t),fe(Document.prototype,"querySelectorAll",t),fe(Element.prototype,"querySelector",t),fe(Element.prototype,"querySelectorAll",t),fe(Element.prototype,"matches",t),fe(Element.prototype,"closest",t),fe(DocumentFragment.prototype,"querySelectorAll",t),Object.defineProperties(HTMLElement.prototype,{popover:{enumerable:!0,configurable:!0,get(){if(!this.hasAttribute("popover"))return null;let l=(this.getAttribute("popover")||"").toLowerCase();return l===""||l=="auto"?"auto":"manual"},set(l){l===null?this.removeAttribute("popover"):this.setAttribute("popover",l)}},showPopover:{enumerable:!0,configurable:!0,value(){Ht(this)}},hidePopover:{enumerable:!0,configurable:!0,value(){Ve(this,!0,!0)}},togglePopover:{enumerable:!0,configurable:!0,value(l){qe.get(this)==="showing"&&l===void 0||l===!1?Ve(this,!0,!0):(l===void 0||l===!0)&&Ht(this)}}});let e=Element.prototype.attachShadow;e&&Object.defineProperties(Element.prototype,{attachShadow:{enumerable:!0,configurable:!0,writable:!0,value(l){let a=e.call(this,l);return Wt(a),a}}});let i=HTMLElement.prototype.attachInternals;i&&Object.defineProperties(HTMLElement.prototype,{attachInternals:{enumerable:!0,configurable:!0,writable:!0,value(){let l=i.call(this);return l.shadowRoot&&Wt(l.shadowRoot),l}}});let s=new WeakMap;function o(l){Object.defineProperties(l.prototype,{popoverTargetElement:{enumerable:!0,configurable:!0,set(a){if(a===null)this.removeAttribute("popovertarget"),s.delete(this);else if(a instanceof Element)this.setAttribute("popovertarget",""),s.set(this,a);else throw new TypeError("popoverTargetElement must be an element or null")},get(){if(this.localName!=="button"&&this.localName!=="input"||this.localName==="input"&&this.type!=="reset"&&this.type!=="image"&&this.type!=="button"||this.disabled||this.form&&this.type==="submit")return null;let a=s.get(this);if(a&&a.isConnected)return a;if(a&&!a.isConnected)return s.delete(this),null;let f=Ee(this),d=this.getAttribute("popovertarget");return(f instanceof Document||f instanceof Vi)&&d&&f.getElementById(d)||null}},popoverTargetAction:{enumerable:!0,configurable:!0,get(){let a=(this.getAttribute("popovertargetaction")||"").toLowerCase();return a==="show"||a==="hide"?a:"toggle"},set(a){this.setAttribute("popovertargetaction",a)}}})}o(HTMLButtonElement),o(HTMLInputElement);let n=l=>{let a=l.composedPath(),f=a[0];if(!(f instanceof Element)||f?.shadowRoot)return;let d=Ee(f);if(!(d instanceof Vi||d instanceof Document))return;let m=a.find(g=>g.matches?.("[popovertargetaction],[popovertarget]"));if(m){Ys(m),l.preventDefault();return}},r=l=>{let a=l.key,f=l.target;!l.defaultPrevented&&f&&(a==="Escape"||a==="Esc")&&gt(f.ownerDocument,!0,!0)};(l=>{l.addEventListener("click",n),l.addEventListener("keydown",r),l.addEventListener("pointerdown",Bi),l.addEventListener("pointerup",Bi)})(document),Wt(document)}function C(t){let e=t({css:(s,...o)=>`@layer base { ${s.raw[0]+o.join("")} }`});if(document.adoptedStyleSheets===void 0){let s=document.createElement("style");s.textContent=e,document.head.appendChild(s);return}let i=new CSSStyleSheet;i.replaceSync(e),document.adoptedStyleSheets=[...document.adoptedStyleSheets,i]}function mt(t,e){let i=t;for(;i;){if(e(i))return i;i=i.parentElement}}function R(t,e){let i=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,e?{acceptNode:s=>{let o,n;return e(s,{skip:()=>o=!0,reject:()=>n=!0}),o?NodeFilter.FILTER_SKIP:n?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT}}:{});return new Vt(i)}var Vt=class{constructor(e){this.walker=e}from(e){return this.walker.currentNode=e,this}first(){return this.walker.firstChild()}last(){return this.walker.lastChild()}next(e){return this.walker.currentNode=e,this.walker.nextSibling()}nextOrFirst(e){let i=this.next(e);return i||(this.walker.currentNode=this.walker.root,this.first())}prev(e){return this.walker.currentNode=e,this.walker.previousSibling()}prevOrLast(e){let i=this.prev(e);return i||(this.walker.currentNode=this.walker.root,this.last())}closest(e,i){let s=this.from(e).walker;for(;s.currentNode;){if(i(s.currentNode))return s.currentNode;s.parentNode()}}contains(e){return this.find(i=>i===e)}find(e){return this.walk((i,s)=>{e(i)&&s(i)})}findOrFirst(e){return this.find(e)||(this.walker.currentNode=this.walker.root),this.first()}each(e){this.walk(i=>e(i))}some(e){return!!this.find(e)}every(e){let i=!0;return this.walk(s=>{e(s)||(i=!1)}),i}map(e){let i=[];return this.walk(s=>i.push(e(s))),i}filter(e){let i=[];return this.walk(s=>e(s)&&i.push(s)),i}walk(e){let i,s=this.walker,o;for(;s.nextNode()&&(i=s.currentNode,e(i,n=>o=n),o===void 0););return o}};function _(t,e){customElements.define(`ui-${t}`,e)}function b(t,e,i,s={}){return t.addEventListener(e,i,s),{off:()=>t.removeEventListener(e,i),pause:o=>{t.removeEventListener(e,i),o(),t.addEventListener(e,i)}}}function Xi(t){return["a[href]","area[href]","input:not([disabled])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","iframe","object","embed","[tabindex]","[contenteditable]"].some(i=>t.matches(i))&&t.tabIndex>=0}function so(t,e){let i;return function(){let s=this,o=arguments;i||(t.apply(s,o),i=!0,setTimeout(()=>i=!1,e))}}var vt="pointer";document.addEventListener("keydown",()=>vt="keyboard",{capture:!0});document.addEventListener("pointerdown",t=>{vt=t.pointerType==="mouse"?"mouse":"touch"},{capture:!0});function $e(){return vt==="keyboard"}function oo(){return vt==="touch"}function wt(t,e){let i="",s=po(()=>{i=""},300);t.addEventListener("keydown",o=>{o.key.length===1&&/[a-zA-Z]/.test(o.key)&&(i+=o.key,e(i),o.stopPropagation()),s()})}function no(t,e){return"lofi-"+(e?e+"-":"")+Math.random().toString(16).slice(2)}function F(t,e){let i=t.hasAttribute("id")?t.getAttribute("id"):no(t,e);return h(t,"id",i),t._x_bindings||(t._x_bindings={}),t._x_bindings.id||(t._x_bindings.id=i),i}function P(){let t=!1;return e=>(...i)=>{t||(t=!0,e(...i),t=!1)}}function Te(t,e,{gain:i,lose:s,focusable:o,useSafeArea:n}){let r=!1;o&&document.addEventListener("focusin",d=>{$e()&&(t.contains(d.target)||e.contains(d.target)?(r=!0,i()):(r=!1,s()))});let c=()=>{},l=()=>{},a=()=>{r=!1,s(),c(),l()},f=()=>{r=!1,c(),l()};return t.addEventListener("pointerenter",d=>{oo()||r||(r=!0,i(),setTimeout(()=>{let{safeArea:m,redraw:g,remove:u}=n?ro(t,e,d.clientX,d.clientY):fo();c=u;let p,v=so(w=>{let A=e.getBoundingClientRect(),x=t.getBoundingClientRect(),O;switch(m.contains(w.target)&&lo(x,A,w.clientX,w.clientY)?O="safeArea":e.contains(w.target)?O="panel":t.contains(w.target)?O="trigger":O="outside",p&&clearTimeout(p),O){case"outside":a();break;case"trigger":g(w.clientX,w.clientY);break;case"panel":c();break;case"safeArea":g(w.clientX,w.clientY),p=setTimeout(()=>{a()},300);break;default:break}},100);document.addEventListener("pointermove",v),l=()=>document.removeEventListener("pointermove",v)}))}),{clear:f}}function ro(t,e,i,s){let o=document.createElement("div"),n=e.getBoundingClientRect(),r=t.getBoundingClientRect();o.style.position="fixed",h(o,"data-safe-area","");let c=(l,a)=>{if(n.top===0&&n.bottom===0)return;let f;n.left<r.left&&(f="left"),n.right>r.right&&(f="right"),n.top<r.top&&n.bottom<a&&(f="up"),n.bottom>r.bottom&&n.top>a&&(f="down"),f===void 0&&(f="right");let d,m,g,u,p,v,w,A,x=10;switch(f){case"left":d=n.right,m=Math.max(n.right,l)+5,g=m-d,u=Math.min(r.top,n.top)-x,p=Math.max(r.bottom,n.bottom)+x,v=p-u,w=a-u,A=`polygon(0% 0%, 100% ${w}px, 0% 100%)`;break;case"right":d=Math.min(n.left,l)-5,m=n.left,g=m-d,u=Math.min(r.top,n.top)-x,p=Math.max(r.bottom,n.bottom)+x,v=p-u,w=a-u,A=`polygon(0% ${w}px, 100% 0%, 100% 100%)`;break;case"up":d=Math.min(l,n.left)-x,m=Math.max(l,n.right)+x,g=m-d,u=n.bottom,p=Math.max(n.bottom,a)+5,v=p-u,w=l-d,A=`polygon(0% 0%, 100% 0%, ${w}px 100%)`;break;case"down":d=Math.min(l,n.left)-x,m=Math.max(l,n.right)+x,g=m-d,u=Math.min(n.top,a)-5,p=n.top,v=p-u,w=l-d,A=`polygon(${w}px 0%, 100% 100%, 0% 100%)`;break}o.style.left=`${d}px`,o.style.top=`${u}px`,o.style.width=`${g}px`,o.style.height=`${v}px`,o.style.clipPath=A};return{safeArea:o,redraw:(l,a)=>{o.isConnected||t.appendChild(o),c(l,a)},remove:()=>{o.remove()}}}function lo(t,e,i,s){return!ao(t,i,s)&&!co(e,i,s)}function ao(t,e,i){return t.left<=e&&e<=t.right&&t.top<=i&&i<=t.bottom}function co(t,e,i){return t.left<=e&&e<=t.right&&t.top<=i&&i<=t.bottom}function h(t,e,i){t._durableAttributeObserver===void 0&&(t._durableAttributeObserver=Gi(t,[e])),t._durableAttributeObserver.hasAttribute(e)||t._durableAttributeObserver.addAttribute(e),t._durableAttributeObserver.pause(()=>{t.setAttribute(e,i)})}function uo(t,e){y(t,e),ho(t,e)}function y(t,e){t._durableAttributeObserver===void 0&&(t._durableAttributeObserver=Gi(t,[e])),t._durableAttributeObserver.hasAttribute(e)||t._durableAttributeObserver.addAttribute(e),t._durableAttributeObserver.pause(()=>{t.removeAttribute(e)})}function ho(t,e){t?._durableAttributeObserver?.hasAttribute(e)&&t._durableAttributeObserver.releaseAttribute(e)}function Gi(t,e){let i=o=>{o.forEach(n=>{n.oldValue===null?t._durableAttributeObserver.pause(()=>y(t,n.attributeName)):t._durableAttributeObserver.pause(()=>h(t,n.attributeName,n.oldValue))})},s=new MutationObserver(o=>i(o));return s.observe(t,{attributeFilter:e,attributeOldValue:!0}),{attributes:e,hasAttribute(o){return this.attributes.includes(o)},addAttribute(o){this.attributes.includes(o)||this.attributes.push(o),s.observe(t,{attributeFilter:this.attributes,attributeOldValue:!0})},releaseAttribute(o){this.hasAttribute(o)&&s.observe(t,{attributeFilter:this.attributes,attributeOldValue:!0})},pause(o){i(s.takeRecords()),s.disconnect(),o(),s.observe(t,{attributeFilter:this.attributes,attributeOldValue:!0})}}}function fo(){return{safeArea:{contains:()=>!1},redraw:()=>{},remove:()=>{}}}function po(t,e){let i;return(...s)=>{clearTimeout(i),i=setTimeout(()=>{t(...s)},e)}}var Ce=0,Oe=!1;C(({css:t})=>t`[data-flux-allow-scroll] { pointer-events: auto; }`);function be(t=null,e=!1){if(e)return{lock:()=>{},unlock:()=>{}};let i=(o=!1)=>{Ki(document.documentElement),bo(document.documentElement,{paddingRight:`calc(${window.innerWidth-document.documentElement.clientWidth}px + ${window.getComputedStyle(document.documentElement).paddingRight})`,overflow:"hidden",...o?{pointerEvents:"none"}:{}}),o&&(h(t,"data-flux-allow-scroll",""),Oe=!0)},s=(o=!1)=>{Ki(document.documentElement),o&&(uo(t,"data-flux-allow-scroll"),Oe=!1)};return{lock(){Ce++,!(Ce>1&&t!==null&&Oe)&&i(t!==null&&!Oe)},unlock(){Ce=Math.max(0,Ce-1),!(Ce>0&&t!==null&&!Oe)&&(s(t!==null&&Oe),Ce>0&&i(!1))}}}function bo(t,e){let i=JSON.parse(t.getAttribute("data-flux-scroll-unlock")||"{}");Object.entries(e).forEach(([s,o])=>{i[s]===void 0&&(i[s]=t.style[s],t.style[s]=o)}),t.setAttribute("data-flux-scroll-unlock",JSON.stringify(i))}function Ki(t){let e=JSON.parse(t.getAttribute("data-flux-scroll-unlock")||"{}");Object.entries(e).forEach(([i,s])=>{t.style[i]=s}),t.removeAttribute("data-flux-scroll-unlock")}function ze(){return document.documentElement.dir==="rtl"}function Ji(){return/^((?!chrome|android).)*safari/i.test(navigator.userAgent)&&!navigator.userAgent.includes("CriOS")&&!navigator.userAgent.includes("FxiOS")}var S=class extends HTMLElement{wasDisconnected=!1;constructor(){super(),this.boot?.()}connectedCallback(){if(this.wasDisconnected){this.wasDisconnected=!1;return}queueMicrotask(()=>{this.mount?.()})}disconnectedCallback(){this.wasDisconnected=!0,queueMicrotask(()=>{this.wasDisconnected&&this.unmount?.(),this.wasDisconnected=!1})}mixin(e,i={}){return new e(this,i)}appendMixin(e,i={}){return new e(this,i)}use(e){let i;return this.mixins.forEach(s=>{s instanceof e&&(i=s)}),i}uses(e){let i;return this.mixins.forEach(s=>{s instanceof e&&(i=!0)}),!!i}on(e,i){return b(this,e,i)}root(e,i={}){if(e===void 0)return this.__root;let s=document.createElement(e);for(let n in i)setAttribute(s,n,i[n]);let o=this.attachShadow({mode:"open"});return s.appendChild(document.createElement("slot")),o.appendChild(s),this.__root=s,this.__root}},I=class extends S{};var E=class{constructor(e,i={}){this.el=e,this.grouped=i.grouped===void 0,this.el.mixins=this.el.mixins?this.el.mixins:new Map,this.el.mixins.set(this.constructor.name,this),this.el[this.constructor.name]=!0,this.el.use||(this.el.use=S.prototype.use.bind(this.el)),this.opts=i,this.boot?.({options:s=>{let o=s;Object.entries(this.opts).forEach(([n,r])=>{r!==void 0&&(o[n]=r)}),this.opts=o}}),queueMicrotask(()=>{this.mount?.()})}options(){return this.opts}hasGroup(){return!!this.group()}group(){if(this.grouped!==!1)return mt(this.el,e=>e[this.groupedByType.name])?.use(this.groupedByType)}on(e,i){return b(this.el,e,i)}},G=class extends E{constructor(e,i={}){super(e,i)}walker(){return R(this.el,(e,{skip:i,reject:s})=>{if(e[this.constructor.name]&&e!==this.el)return s();if(!e[this.groupOfType.name]||!e.mixins.get(this.groupOfType.name).grouped)return i()})}};var L=class extends E{boot({options:e}){e({bubbles:!1}),this.initialState=this.el.value,this.getterFunc=()=>{},this.setterFunc=i=>this.initialState=i,Object.defineProperty(this.el,"value",{get:()=>this.getterFunc(),set:i=>{this.setterFunc(i)}})}initial(e){e(this.initialState)}getter(e){this.getterFunc=e}setter(e){this.setterFunc=e}dispatch(){this.el.dispatchEvent(new Event("input",{bubbles:this.options().bubbles,cancelable:!0})),this.el.dispatchEvent(new Event("change",{bubbles:this.options().bubbles,cancelable:!0}))}};var je=null;document.addEventListener("mousedown",t=>je=t);var yt=class extends E{boot({options:e}){e({clickOutside:!0,triggers:[]}),this.onChanges=[],this.state=!1,this.stopDialogFromFocusingTheFirstElement();let i=this.options().triggers;new MutationObserver(o=>{o.forEach(n=>{n.attributeName==="open"&&(this.el.hasAttribute("open")?this.state=!0:this.state=!1)}),this.onChanges.forEach(n=>n())}).observe(this.el,{attributeFilter:["open"]}),this.options().clickOutside&&this.el.addEventListener("click",o=>{if(o.target!==this.el){je=null;return}je&&Qi(this.el,je)&&Qi(this.el,o)&&(this.cancel(),o.preventDefault(),o.stopPropagation()),je=null}),this.el.hasAttribute("open")&&(this.state=!0,this.hide(),this.show())}onChange(e){this.onChanges.push(e)}show(){this.el.showModal()}hide(){this.el.close()}toggle(){this.state?this.hide():this.show()}cancel(){let e=new Event("cancel",{bubbles:!1,cancelable:!0});this.el.dispatchEvent(e),e.defaultPrevented||this.hide()}getState(){return this.state}setState(e){e?this.show():this.hide()}stopDialogFromFocusingTheFirstElement(){let e=document.createElement("div");e.setAttribute("data-flux-focus-placeholder",""),e.setAttribute("data-appended",""),e.setAttribute("tabindex","0"),this.el.prepend(e),this.onChange(()=>{h(e,"style",this.state?"display: none":"display: block"),this.state&&Ji()&&!this.el.hasAttribute("autofocus")&&this.el.querySelectorAll("[autofocus]").length===0&&setTimeout(()=>{this.el.setAttribute("tabindex","-1"),this.el.focus(),this.el.blur()})})}};function Qi(t,e){let i=t.getBoundingClientRect(),s=e.clientX,o=e.clientY;return!(s>=i.left&&s<=i.right&&o>=i.top&&o<=i.bottom)}var qt=class extends S{boot(){this.querySelectorAll("[data-appended]").forEach(c=>c.remove()),this._controllable=new L(this,{disabled:this.hasAttribute("disabled")});let e=this.button(),i=this.dialog();if(!i)return;i._dialogable=new yt(i,{clickOutside:!this.hasAttribute("disable-click-outside")}),this._controllable.initial(c=>c&&i._dialogable.show()),this._controllable.getter(()=>i._dialogable.getState());let s=P();this._controllable.setter(s(c=>{i._dialogable.setState(c)})),i._dialogable.onChange(s(()=>{this._controllable.dispatch()}));let o=()=>{i._dialogable.getState()?(h(this,"data-open",""),e?.setAttribute("data-open",""),h(i,"data-open","")):(y(this,"data-open"),e?.removeAttribute("data-open"),y(i,"data-open"))};i._dialogable.onChange(()=>o()),o();let{lock:n,unlock:r}=be();i._dialogable.onChange(()=>{i._dialogable.getState()?n():r()}),e&&b(e,"click",c=>{i._dialogable.show()})}unmount(){let{unlock:e}=be();this.dialog()._dialogable.getState()&&e()}button(){let e=this.querySelector("button");if(!this.dialog()?.contains(e))return e}dialog(){return this.querySelector("dialog")}showModal(){let e=this.dialog();e&&e.showModal()}},$t=class extends S{mount(){let e=this.querySelector("button");b(e,"click",()=>{mt(this,s=>!!s._dialogable)?._dialogable?.hide()})}};C(({css:t})=>t`dialog, ::backdrop { margin: auto; }`);_("modal",qt);_("close",$t);var At=class extends E{boot({options:e}){this.onChanges=[],this.state=!1}onChange(e){this.onChanges.push(e)}getState(){return this.state}setState(e){let i=this.state;this.state=!!e,this.state!==i&&this.onChanges.forEach(s=>s())}};var zt=class extends S{boot(){let e=this.button(),i=this.details();if(e){if(!i)return console.warn("ui-disclosure: no panel element found",this)}else return console.warn("ui-disclosure: no trigger element found",this);this._disabled=this.hasAttribute("disabled"),this._controllable=new L(this,{disabled:this._disabled}),i._disclosable=new At(i),this._controllable.initial(n=>n&&i._disclosable.setState(!0)),this._controllable.getter(()=>i._disclosable.getState()),this._controllable.setter(n=>i._disclosable.setState(n)),i._disclosable.onChange(()=>{this.dispatchEvent(new CustomEvent("lofi-disclosable-change",{bubbles:!0})),this._controllable.dispatch()});let s=()=>{i._disclosable.getState()?(h(this,"data-open",""),h(e,"data-open",""),h(i,"data-open","")):(y(this,"data-open"),y(e,"data-open"),y(i,"data-open"))};i._disclosable.onChange(()=>s()),s(),this._disabled||b(e,"click",n=>{i._disclosable.setState(!i._disclosable.getState())});let o=F(i,"disclosure");h(e,"aria-controls",o),h(e,"aria-expanded","false"),i._disclosable.onChange(()=>{i._disclosable.getState()?h(e,"aria-expanded","true"):h(e,"aria-expanded","false")}),this.hasAttribute("open")&&i._disclosable.setState(!0)}button(){return this.querySelector("button")}details(){return this.lastElementChild}},jt=class t extends S{boot(){this.exclusive=this.hasAttribute("exclusive"),this.exclusive&&b(this,"lofi-disclosable-change",e=>{e.stopPropagation(),e.target.localName==="ui-disclosure"&&e.target.value&&this.disclosureWalker().each(i=>{i!==e.target&&(i.value=!1)})})}disclosureWalker(){return R(this,(e,{skip:i,reject:s})=>{if(e instanceof t&&e!==this||e.localName!=="ui-disclosure")return s()})}};C(({css:t})=>t`ui-disclosure { display: block; }`);_("disclosure",zt);_("disclosure-group",jt);var B=class extends G{groupOfType=k;boot({options:e}){e({multiple:!1}),this.state=this.options().multiple?new Set:null,this.onChanges=[]}onInitAndChange(e){e(),this.onChanges.push(e)}onChange(e){this.onChanges.push(e)}changed(e,i=!1){if(e.ungrouped)return;let s=e.value,o=e.isSelected(),n=this.options().multiple;o?n?this.state.add(s):this.state=s:n?this.state.delete(s):this.state=null,i||this.onChanges.forEach(r=>r())}getState(){return this.options().multiple?Array.from(this.state):this.state}hasValue(e){return this.options().multiple?this.state.has(e):this.state===e}setState(e){(e===null||e==="")&&(e=this.options().multiple?[]:""),this.options().multiple?(Array.isArray(e)||(e=[e]),e=e.map(s=>s+"")):e=e+"",this.state=this.options().multiple?new Set(e):e;let i=this.options().multiple?e:[e];this.walker().each(s=>{let o=s.use(k);if(o.ungrouped)return;let n=i.includes(o.value);n&&!o.isSelected()?o.surgicallySelect():!n&&o.isSelected()&&o.surgicallyDeselect()}),this.onChanges.forEach(s=>s())}selected(){return this.walker().find(e=>e.use(k).isSelected()).use(k)}selecteds(){return this.walker().filter(e=>e.use(k).isSelected()).map(e=>e.use(k))}selectFirst(){this.walker().first()?.use(k).select()}selectAll(){this.walker().filter(e=>!e.use(k).isSelected()).map(e=>e.use(k).select())}deselectAll(){this.walker().filter(e=>e.use(k).isSelected()).map(e=>e.use(k).deselect())}allAreSelected(){return this.walker().filter(e=>e.use(k).isSelected()).length===this.walker().filter(e=>!0).length}noneAreSelected(){return this.state===null||this.state?.size===0}selectableByValue(e){return this.walker().find(i=>i.use(k).value===e)?.use(k)}deselectOthers(e){this.walker().each(i=>{i!==e&&i.use(k).surgicallyDeselect()})}selectedTextValue(){return this.options().multiple?Array.from(this.state).map(e=>this.convertValueStringToElementText(e)).join(", "):this.convertValueStringToElementText(this.state)}convertValueStringToElementText(e){let i=this.findByValue(e);return i?i.label||i.value:e}findByValue(e){return this.selecteds().find(i=>i.value===e)}walker(){return R(this.el,(e,{skip:i,reject:s})=>{if(e[this.constructor.name]&&e!==this.el)return s();if(!e[this.groupOfType.name]||e.mixins.get(this.groupOfType.name).ungrouped)return i()})}},k=class extends E{boot({options:e}){this.groupedByType=B,e({ungrouped:!1,togglable:!1,value:void 0,label:void 0,selectedInitially:!1,dataAttr:"data-selected",ariaAttr:"aria-selected"}),this.ungrouped=this.options().ungrouped,this.value=this.options().value===void 0?this.el.value:this.options().value,this.value=this.value+"",this.label=this.options().label;let i=this.options().selectedInitially;this.onSelects=[],this.onUnselects=[],this.onChanges=[];let s=()=>{this.group()&&this.group().hasValue(this.value)&&(i=!0),this.multiple=this.hasGroup()?this.group().options().multiple:!1,this.toggleable=this.options().toggleable||this.multiple,i?this.select(!0):(this.state=i,this.surgicallyDeselect(!0))};this.el.isConnected?s():queueMicrotask(s)}mount(){this.el.hasAttribute(this.options().ariaAttr)||h(this.el,this.options().ariaAttr,"false")}onInitAndChange(e){e(),this.onChanges.push(e)}onChange(e){this.onChanges.push(e)}onSelect(e){this.onSelects.push(e)}onUnselect(e){this.onUnselects.push(e)}setState(e){e?this.select():this.deselect()}getState(){return this.state}press(){this.toggleable?this.toggle():this.select()}trigger(){this.toggleable?this.toggle():this.select()}toggle(){this.isSelected()?this.deselect():this.select()}isSelected(){return this.state}select(e=!1){let i=!this.isSelected();this.toggleable||this.group()?.deselectOthers(this.el),this.state=!0,h(this.el,this.options().ariaAttr,"true"),h(this.el,this.options().dataAttr,""),i&&(e||(this.onSelects.forEach(s=>s()),this.onChanges.forEach(s=>s())),this.group()?.changed(this,e))}surgicallySelect(){let e=!this.isSelected();this.state=!0,h(this.el,this.options().ariaAttr,"true"),h(this.el,this.options().dataAttr,""),e&&(this.onSelects.forEach(i=>i()),this.onChanges.forEach(i=>i()))}deselect(e=!0){let i=this.isSelected();this.state=!1,h(this.el,this.options().ariaAttr,"false"),y(this.el,this.options().dataAttr),i&&(this.onUnselects.forEach(s=>s()),this.onChanges.forEach(s=>s()),e&&this.group()?.changed(this))}surgicallyDeselect(e=!1){let i=this.isSelected();this.state=!1,h(this.el,this.options().ariaAttr,"false"),y(this.el,this.options().dataAttr),i&&!e&&(this.onUnselects.forEach(s=>s()),this.onChanges.forEach(s=>s()))}getValue(){return this.value}getLabel(){return this.label}};var N=class extends E{boot({options:e}){e({disableWithParent:!0}),this.onChanges=[],Object.defineProperty(this.el,"disabled",{get:()=>this.el.hasAttribute("disabled"),set:s=>{s?this.el.setAttribute("disabled",""):this.el.removeAttribute("disabled")}}),this.el.hasAttribute("disabled")?this.el.disabled=!0:this.options().disableWithParent&&this.el.closest("[disabled]")&&(this.el.disabled=!0),new MutationObserver(s=>{this.onChanges.forEach(o=>o(this.el.disabled))}).observe(this.el,{attributeFilter:["disabled"]})}onChange(e){this.onChanges.push(e)}onInitAndChange(e){e(this.el.disabled),this.onChanges.push(e)}enabled(e){return(...i)=>{if(!this.el.disabled)return e(...i)}}disabled(e){return(...i)=>{if(this.el.disabled)return e(...i)}}isDisabled(){return this.el.disabled}};var j=class extends E{boot({options:e}){e({name:void 0,value:void 0,includeWhenEmpty:!0,shouldUpdateValue:!0}),this.name=this.options().name,this.value=this.options().value===void 0?this.el.value:this.options().value,this.state=!1,this.observer=new MutationObserver(()=>{this.renderHiddenInputs()}),this.observer.observe(this.el,{childList:!0})}mount(){this.renderHiddenInputs()}update(e){this.options().shouldUpdateValue?this.value=e:this.state=!!e,this.renderHiddenInputs()}valueIsEmpty(){return this.value===void 0||this.value===null||this.value===""}renderHiddenInputs(){if(this.observer.disconnect(),!this.name)return;let e=this.el.children,i=[];for(let o=0;o<e.length;o++){let n=e[o];n.hasAttribute("data-flux-hidden")&&i.push(n)}i.forEach(o=>o.remove());let s;this.options().shouldUpdateValue?s=!this.valueIsEmpty()||this.options().includeWhenEmpty?this.generateInputs(this.name,this.value):[]:s=this.state||this.options().includeWhenEmpty?this.generateInputs(this.name,this.value):[],s.forEach(o=>{this.el.append(o)}),this.observer.observe(this.el,{childList:!0})}generateInputs(e,i,s=[]){if(this.isObjectOrArray(i))for(let o in i)s=s.concat(this.generateInputs(`${e}[${o}]`,i[o]));else{let o=document.createElement("input");return o.setAttribute("type","hidden"),o.setAttribute("name",e),o.setAttribute("value",i===null?"":""+i),o.setAttribute("data-flux-hidden",""),o.setAttribute("data-appended",""),[o]}return s}isObjectOrArray(e){return typeof e=="object"&&e!==null}};var Ut=class t extends I{boot(){this._disableable=new N(this);let e=[];this._disableable.onInitAndChange(i=>{i?this.walker().each(s=>{s.hasAttribute("disabled")||(s.setAttribute("disabled",""),e.push(()=>s.removeAttribute("disabled")))}):(e.forEach(s=>s()),e=[])}),this._selectable=new B(this,{multiple:!0}),this._controllable=new L(this,{disabled:this._disabled,bubbles:!0}),this.walker().each(i=>{i.addEventListener("input",s=>s.stopPropagation()),i.addEventListener("change",s=>s.stopPropagation())}),this._submittable=new j(this,{name:this.getAttribute("name"),value:this.getAttribute("value"),includeWhenEmpty:!1}),this._controllable.initial(i=>i&&this._selectable.setState(i)),this._controllable.getter(()=>this._selectable.getState()),this._detangled=P(),this._controllable.setter(this._detangled(i=>{this._selectable.setState(i)})),this._selectable.onChange(this._detangled(()=>{this._controllable.dispatch()})),this._selectable.onInitAndChange(()=>{this._submittable.update(this._selectable.getState())}),h(this,"role","group"),queueMicrotask(()=>{this._submittable.update(this._selectable.getState())})}initCheckAll(e){let i=P();e._selectable.onChange(i(()=>{e.indeterminate?(this._selectable.selectAll(),e.checked=!0,e.indeterminate=!1):e.checked?(this._selectable.selectAll(),e.checked=!0,e.indeterminate=!1):(this._selectable.deselectAll(),e.checked=!1,e.indeterminate=!1)}));let s=()=>{this._selectable.allAreSelected()?(e.indeterminate=!1,e._selectable.select()):this._selectable.noneAreSelected()?(e.indeterminate=!1,e._selectable.deselect()):e.indeterminate=!0};this._selectable.onChange(i(()=>{s()})),s()}walker(){return R(this,(e,{skip:i,reject:s})=>{if(e instanceof t)return s();if(e.localName!=="ui-checkbox")return i()})}},Yt=class extends I{boot(){let e=this;this.isIndeterminate=!1,this._disableable=new N(this),this.hasAttribute("all")?(this._selectable=new k(e,{ungrouped:!0,toggleable:!0,value:this.hasAttribute("value")?this.getAttribute("value"):Math.random().toString(36).substring(2,10),label:this.hasAttribute("label")?this.getAttribute("label"):null,selectedInitially:this.hasAttribute("checked"),dataAttr:"data-checked",ariaAttr:"aria-checked"}),queueMicrotask(()=>{this.closest("ui-checkbox-group")?.initCheckAll(this)})):(this._selectable=new k(e,{toggleable:!0,dataAttr:"data-checked",ariaAttr:"aria-checked",value:this.hasAttribute("value")?this.getAttribute("value"):Math.random().toString(36).substring(2,10),label:this.hasAttribute("label")?this.getAttribute("label"):null,selectedInitially:this.hasAttribute("checked")}),this._submittable=new j(this,{name:this.getAttribute("name"),value:this.getAttribute("value")??"on",includeWhenEmpty:!1,shouldUpdateValue:!1}),this._selectable.onChange(()=>{this.indeterminate&&(this.indeterminate=!1)}),this._selectable.onInitAndChange(()=>{this._submittable.update(this._selectable.isSelected())}),this.value=this._selectable.getValue(),queueMicrotask(()=>{this._submittable.update(this._selectable.isSelected())})),this._detangled=P(),this._selectable.onChange(this._detangled(()=>{this.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!0})),this.dispatchEvent(new Event("change",{bubbles:!0,cancelable:!0}))})),h(e,"role","checkbox"),this._disableable.onInitAndChange(i=>{i?y(e,"tabindex","0"):h(e,"tabindex","0")}),b(e,"click",this._disableable.disabled(i=>{i.preventDefault(),i.stopPropagation()}),{capture:!0}),b(e,"click",this._disableable.enabled(i=>{this._selectable.press()})),b(e,"keydown",this._disableable.enabled(i=>{i.key==="Enter"&&(this._selectable.press(),i.preventDefault(),i.stopPropagation())})),b(e,"keydown",this._disableable.enabled(i=>{i.key===" "&&(i.preventDefault(),i.stopPropagation())})),b(e,"keyup",this._disableable.enabled(i=>{i.key===" "&&(this._selectable.press(),i.preventDefault(),i.stopPropagation())})),go(e)}get checked(){return this._selectable.isSelected()}set checked(e){let i=this.closest("ui-checkbox-group")?._detangled||(s=>s);this._detangled(i(()=>{e?this._selectable.select():this._selectable.deselect()}))()}get indeterminate(){return this.isIndeterminate}set indeterminate(e){this.isIndeterminate=!!e,this.isIndeterminate?h(this,"data-indeterminate",""):y(this,"data-indeterminate")}};_("checkbox-group",Ut);_("checkbox",Yt);C(({css:t})=>t`ui-checkbox-group { display: block; user-select: none; }`);C(({css:t})=>t`ui-checkbox { display: inline-block; user-select: none; }`);function go(t){t.closest("label")?.addEventListener("click",e=>{t.contains(e.target)||t._selectable.press()})}var Z=new Map,V=class extends E{boot({options:e}){e({triggers:[],scope:null});let i=this.options().scope||"global";h(this.el,"popover","manual"),this.triggers=this.options().triggers,this.onChanges=[],this.state=!1,b(this.el,"beforetoggle",s=>{let o=this.state;if(this.state=s.newState==="open",this.state){mo(this.el,i);let n=new AbortController,r=document.activeElement,c=[...this.triggers,r];setTimeout(()=>{vo(this.el,c,n),wo(this.el,c,n),yo(this.el,c,n)}),this.el.addEventListener("beforetoggle",l=>{l.newState==="closed"&&(n.abort(),r?.focus())},{signal:n.signal})}o!==this.state&&this.onChanges.forEach(n=>n(this.state,o))}),b(this.el,"toggle",s=>{if(s.newState==="open")Z.has(i)||Z.set(i,new Set),Z.get(i).add(this.el);else if(s.newState==="closed"){if(!Z.has(i))return;Z.get(i).delete(this.el),Z.get(i).size===0&&Z.delete(i)}})}onChange(e){this.onChanges.push(e)}setState(e){e?this.show():this.hide()}getState(){return this.state}toggle(){this.el.togglePopover()}show(){this.el.showPopover()}hide(){this.el.hidePopover()}};function mo(t,e){Z.has(e)&&Z.get(e).forEach(i=>{t.contains(i)||i.contains(t)||i.hidePopover()})}function vo(t,e,i){document.addEventListener("click",s=>{t.contains(s.target)||e.includes(s.target)||t.hidePopover()},{signal:i.signal})}function wo(t,e,i){document.addEventListener("focusin",s=>{t.contains(s.target)||e.includes(s.target)||(i.abort(),t.hidePopover())},{capture:!0,signal:i.signal})}function yo(t,e,i){document.addEventListener("keydown",s=>{s.key==="Escape"&&t.hidePopover()},{signal:i.signal})}var ne=Math.min,W=Math.max,Ye=Math.round,Ke=Math.floor,U=t=>({x:t,y:t}),Ao={left:"right",right:"left",bottom:"top",top:"bottom"},xo={start:"end",end:"start"};function Kt(t,e,i){return W(t,ne(e,i))}function Pe(t,e){return typeof t=="function"?t(e):t}function ee(t){return t.split("-")[0]}function Le(t){return t.split("-")[1]}function Xt(t){return t==="x"?"y":"x"}function Gt(t){return t==="y"?"height":"width"}function re(t){return["top","bottom"].includes(ee(t))?"y":"x"}function Jt(t){return Xt(re(t))}function Zi(t,e,i){i===void 0&&(i=!1);let s=Le(t),o=Jt(t),n=Gt(o),r=o==="x"?s===(i?"end":"start")?"right":"left":s==="start"?"bottom":"top";return e.reference[n]>e.floating[n]&&(r=Ue(r)),[r,Ue(r)]}function es(t){let e=Ue(t);return[xt(t),e,xt(e)]}function xt(t){return t.replace(/start|end/g,e=>xo[e])}function _o(t,e,i){let s=["left","right"],o=["right","left"],n=["top","bottom"],r=["bottom","top"];switch(t){case"top":case"bottom":return i?e?o:s:e?s:o;case"left":case"right":return e?n:r;default:return[]}}function ts(t,e,i,s){let o=Le(t),n=_o(ee(t),i==="start",s);return o&&(n=n.map(r=>r+"-"+o),e&&(n=n.concat(n.map(xt)))),n}function Ue(t){return t.replace(/left|right|bottom|top/g,e=>Ao[e])}function So(t){return{top:0,right:0,bottom:0,left:0,...t}}function is(t){return typeof t!="number"?So(t):{top:t,right:t,bottom:t,left:t}}function ge(t){let{x:e,y:i,width:s,height:o}=t;return{width:s,height:o,top:i,left:e,right:e+s,bottom:i+o,x:e,y:i}}function ss(t,e,i){let{reference:s,floating:o}=t,n=re(e),r=Jt(e),c=Gt(r),l=ee(e),a=n==="y",f=s.x+s.width/2-o.width/2,d=s.y+s.height/2-o.height/2,m=s[c]/2-o[c]/2,g;switch(l){case"top":g={x:f,y:s.y-o.height};break;case"bottom":g={x:f,y:s.y+s.height};break;case"right":g={x:s.x+s.width,y:d};break;case"left":g={x:s.x-o.width,y:d};break;default:g={x:s.x,y:s.y}}switch(Le(e)){case"start":g[r]-=m*(i&&a?-1:1);break;case"end":g[r]+=m*(i&&a?-1:1);break}return g}var os=async(t,e,i)=>{let{placement:s="bottom",strategy:o="absolute",middleware:n=[],platform:r}=i,c=n.filter(Boolean),l=await(r.isRTL==null?void 0:r.isRTL(e)),a=await r.getElementRects({reference:t,floating:e,strategy:o}),{x:f,y:d}=ss(a,s,l),m=s,g={},u=0;for(let p=0;p<c.length;p++){let{name:v,fn:w}=c[p],{x:A,y:x,data:O,reset:T}=await w({x:f,y:d,initialPlacement:s,placement:m,strategy:o,middlewareData:g,rects:a,platform:r,elements:{reference:t,floating:e}});f=A??f,d=x??d,g={...g,[v]:{...g[v],...O}},T&&u<=50&&(u++,typeof T=="object"&&(T.placement&&(m=T.placement),T.rects&&(a=T.rects===!0?await r.getElementRects({reference:t,floating:e,strategy:o}):T.rects),{x:f,y:d}=ss(a,m,l)),p=-1)}return{x:f,y:d,placement:m,strategy:o,middlewareData:g}};async function _t(t,e){var i;e===void 0&&(e={});let{x:s,y:o,platform:n,rects:r,elements:c,strategy:l}=t,{boundary:a="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:m=!1,padding:g=0}=Pe(e,t),u=is(g),v=c[m?d==="floating"?"reference":"floating":d],w=ge(await n.getClippingRect({element:(i=await(n.isElement==null?void 0:n.isElement(v)))==null||i?v:v.contextElement||await(n.getDocumentElement==null?void 0:n.getDocumentElement(c.floating)),boundary:a,rootBoundary:f,strategy:l})),A=d==="floating"?{x:s,y:o,width:r.floating.width,height:r.floating.height}:r.reference,x=await(n.getOffsetParent==null?void 0:n.getOffsetParent(c.floating)),O=await(n.isElement==null?void 0:n.isElement(x))?await(n.getScale==null?void 0:n.getScale(x))||{x:1,y:1}:{x:1,y:1},T=ge(n.convertOffsetParentRelativeRectToViewportRelativeRect?await n.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:A,offsetParent:x,strategy:l}):A);return{top:(w.top-T.top+u.top)/O.y,bottom:(T.bottom-w.bottom+u.bottom)/O.y,left:(w.left-T.left+u.left)/O.x,right:(T.right-w.right+u.right)/O.x}}var ns=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var i,s;let{placement:o,middlewareData:n,rects:r,initialPlacement:c,platform:l,elements:a}=e,{mainAxis:f=!0,crossAxis:d=!0,fallbackPlacements:m,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:u="none",flipAlignment:p=!0,...v}=Pe(t,e);if((i=n.arrow)!=null&&i.alignmentOffset)return{};let w=ee(o),A=re(c),x=ee(c)===c,O=await(l.isRTL==null?void 0:l.isRTL(a.floating)),T=m||(x||!p?[Ue(c)]:es(c)),le=u!=="none";!m&&le&&T.push(...ts(c,p,u,O));let X=[c,...T],ae=await _t(e,v),ce=[],ie=((s=n.flip)==null?void 0:s.overflows)||[];if(f&&ce.push(ae[w]),d){let ue=Zi(o,r,O);ce.push(ae[ue[0]],ae[ue[1]])}if(ie=[...ie,{placement:o,overflows:ce}],!ce.every(ue=>ue<=0)){var Fe,Ie;let ue=(((Fe=n.flip)==null?void 0:Fe.index)||0)+1,Oi=X[ue];if(Oi)return{data:{index:ue,overflows:ie},reset:{placement:Oi}};let We=(Ie=ie.filter(xe=>xe.overflows[0]<=0).sort((xe,se)=>xe.overflows[1]-se.overflows[1])[0])==null?void 0:Ie.placement;if(!We)switch(g){case"bestFit":{var Ne;let xe=(Ne=ie.filter(se=>{if(le){let oe=re(se.placement);return oe===A||oe==="y"}return!0}).map(se=>[se.placement,se.overflows.filter(oe=>oe>0).reduce((oe,Ms)=>oe+Ms,0)]).sort((se,oe)=>se[1]-oe[1])[0])==null?void 0:Ne[0];xe&&(We=xe);break}case"initialPlacement":We=c;break}if(o!==We)return{reset:{placement:We}}}return{}}}};async function ko(t,e){let{placement:i,platform:s,elements:o}=t,n=await(s.isRTL==null?void 0:s.isRTL(o.floating)),r=ee(i),c=Le(i),l=re(i)==="y",a=["left","top"].includes(r)?-1:1,f=n&&l?-1:1,d=Pe(e,t),{mainAxis:m,crossAxis:g,alignmentAxis:u}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return c&&typeof u=="number"&&(g=c==="end"?u*-1:u),l?{x:g*f,y:m*a}:{x:m*a,y:g*f}}var rs=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(e){var i,s;let{x:o,y:n,placement:r,middlewareData:c}=e,l=await ko(e,t);return r===((i=c.offset)==null?void 0:i.placement)&&(s=c.arrow)!=null&&s.alignmentOffset?{}:{x:o+l.x,y:n+l.y,data:{...l,placement:r}}}}},ls=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){let{x:i,y:s,placement:o}=e,{mainAxis:n=!0,crossAxis:r=!1,limiter:c={fn:v=>{let{x:w,y:A}=v;return{x:w,y:A}}},...l}=Pe(t,e),a={x:i,y:s},f=await _t(e,l),d=re(ee(o)),m=Xt(d),g=a[m],u=a[d];if(n){let v=m==="y"?"top":"left",w=m==="y"?"bottom":"right",A=g+f[v],x=g-f[w];g=Kt(A,g,x)}if(r){let v=d==="y"?"top":"left",w=d==="y"?"bottom":"right",A=u+f[v],x=u-f[w];u=Kt(A,u,x)}let p=c.fn({...e,[m]:g,[d]:u});return{...p,data:{x:p.x-i,y:p.y-s,enabled:{[m]:n,[d]:r}}}}}};var as=function(t){return t===void 0&&(t={}),{name:"size",options:t,async fn(e){var i,s;let{placement:o,rects:n,platform:r,elements:c}=e,{apply:l=()=>{},...a}=Pe(t,e),f=await _t(e,a),d=ee(o),m=Le(o),g=re(o)==="y",{width:u,height:p}=n.floating,v,w;d==="top"||d==="bottom"?(v=d,w=m===(await(r.isRTL==null?void 0:r.isRTL(c.floating))?"start":"end")?"left":"right"):(w=d,v=m==="end"?"top":"bottom");let A=p-f.top-f.bottom,x=u-f.left-f.right,O=ne(p-f[v],A),T=ne(u-f[w],x),le=!e.middlewareData.shift,X=O,ae=T;if((i=e.middlewareData.shift)!=null&&i.enabled.x&&(ae=x),(s=e.middlewareData.shift)!=null&&s.enabled.y&&(X=A),le&&!m){let ie=W(f.left,0),Fe=W(f.right,0),Ie=W(f.top,0),Ne=W(f.bottom,0);g?ae=u-2*(ie!==0||Fe!==0?ie+Fe:W(f.left,f.right)):X=p-2*(Ie!==0||Ne!==0?Ie+Ne:W(f.top,f.bottom))}await l({...e,availableWidth:ae,availableHeight:X});let ce=await r.getDimensions(c.floating);return u!==ce.width||p!==ce.height?{reset:{rects:!0}}:{}}}};function St(){return typeof window<"u"}function me(t){return us(t)?(t.nodeName||"").toLowerCase():"#document"}function H(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function Y(t){var e;return(e=(us(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function us(t){return St()?t instanceof Node||t instanceof H(t).Node:!1}function q(t){return St()?t instanceof Element||t instanceof H(t).Element:!1}function K(t){return St()?t instanceof HTMLElement||t instanceof H(t).HTMLElement:!1}function cs(t){return!St()||typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof H(t).ShadowRoot}function De(t){let{overflow:e,overflowX:i,overflowY:s,display:o}=$(t);return/auto|scroll|overlay|hidden|clip/.test(e+s+i)&&!["inline","contents"].includes(o)}function hs(t){return["table","td","th"].includes(me(t))}function Xe(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch{return!1}})}function kt(t){let e=Et(),i=q(t)?$(t):t;return["transform","translate","scale","rotate","perspective"].some(s=>i[s]?i[s]!=="none":!1)||(i.containerType?i.containerType!=="normal":!1)||!e&&(i.backdropFilter?i.backdropFilter!=="none":!1)||!e&&(i.filter?i.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(s=>(i.willChange||"").includes(s))||["paint","layout","strict","content"].some(s=>(i.contain||"").includes(s))}function ds(t){let e=te(t);for(;K(e)&&!ve(e);){if(kt(e))return e;if(Xe(e))return null;e=te(e)}return null}function Et(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function ve(t){return["html","body","#document"].includes(me(t))}function $(t){return H(t).getComputedStyle(t)}function Ge(t){return q(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function te(t){if(me(t)==="html")return t;let e=t.assignedSlot||t.parentNode||cs(t)&&t.host||Y(t);return cs(e)?e.host:e}function fs(t){let e=te(t);return ve(e)?t.ownerDocument?t.ownerDocument.body:t.body:K(e)&&De(e)?e:fs(e)}function Me(t,e,i){var s;e===void 0&&(e=[]),i===void 0&&(i=!0);let o=fs(t),n=o===((s=t.ownerDocument)==null?void 0:s.body),r=H(o);if(n){let c=Ct(r);return e.concat(r,r.visualViewport||[],De(o)?o:[],c&&i?Me(c):[])}return e.concat(o,Me(o,[],i))}function Ct(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function gs(t){let e=$(t),i=parseFloat(e.width)||0,s=parseFloat(e.height)||0,o=K(t),n=o?t.offsetWidth:i,r=o?t.offsetHeight:s,c=Ye(i)!==n||Ye(s)!==r;return c&&(i=n,s=r),{width:i,height:s,$:c}}function Zt(t){return q(t)?t:t.contextElement}function Re(t){let e=Zt(t);if(!K(e))return U(1);let i=e.getBoundingClientRect(),{width:s,height:o,$:n}=gs(e),r=(n?Ye(i.width):i.width)/s,c=(n?Ye(i.height):i.height)/o;return(!r||!Number.isFinite(r))&&(r=1),(!c||!Number.isFinite(c))&&(c=1),{x:r,y:c}}var Eo=U(0);function ms(t){let e=H(t);return!Et()||!e.visualViewport?Eo:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function Co(t,e,i){return e===void 0&&(e=!1),!i||e&&i!==H(t)?!1:e}function we(t,e,i,s){e===void 0&&(e=!1),i===void 0&&(i=!1);let o=t.getBoundingClientRect(),n=Zt(t),r=U(1);e&&(s?q(s)&&(r=Re(s)):r=Re(t));let c=Co(n,i,s)?ms(n):U(0),l=(o.left+c.x)/r.x,a=(o.top+c.y)/r.y,f=o.width/r.x,d=o.height/r.y;if(n){let m=H(n),g=s&&q(s)?H(s):s,u=m,p=Ct(u);for(;p&&s&&g!==u;){let v=Re(p),w=p.getBoundingClientRect(),A=$(p),x=w.left+(p.clientLeft+parseFloat(A.paddingLeft))*v.x,O=w.top+(p.clientTop+parseFloat(A.paddingTop))*v.y;l*=v.x,a*=v.y,f*=v.x,d*=v.y,l+=x,a+=O,u=H(p),p=Ct(u)}}return ge({width:f,height:d,x:l,y:a})}function ei(t,e){let i=Ge(t).scrollLeft;return e?e.left+i:we(Y(t)).left+i}function vs(t,e,i){i===void 0&&(i=!1);let s=t.getBoundingClientRect(),o=s.left+e.scrollLeft-(i?0:ei(t,s)),n=s.top+e.scrollTop;return{x:o,y:n}}function Oo(t){let{elements:e,rect:i,offsetParent:s,strategy:o}=t,n=o==="fixed",r=Y(s),c=e?Xe(e.floating):!1;if(s===r||c&&n)return i;let l={scrollLeft:0,scrollTop:0},a=U(1),f=U(0),d=K(s);if((d||!d&&!n)&&((me(s)!=="body"||De(r))&&(l=Ge(s)),K(s))){let g=we(s);a=Re(s),f.x=g.x+s.clientLeft,f.y=g.y+s.clientTop}let m=r&&!d&&!n?vs(r,l,!0):U(0);return{width:i.width*a.x,height:i.height*a.y,x:i.x*a.x-l.scrollLeft*a.x+f.x+m.x,y:i.y*a.y-l.scrollTop*a.y+f.y+m.y}}function To(t){return Array.from(t.getClientRects())}function Po(t){let e=Y(t),i=Ge(t),s=t.ownerDocument.body,o=W(e.scrollWidth,e.clientWidth,s.scrollWidth,s.clientWidth),n=W(e.scrollHeight,e.clientHeight,s.scrollHeight,s.clientHeight),r=-i.scrollLeft+ei(t),c=-i.scrollTop;return $(s).direction==="rtl"&&(r+=W(e.clientWidth,s.clientWidth)-o),{width:o,height:n,x:r,y:c}}function Lo(t,e){let i=H(t),s=Y(t),o=i.visualViewport,n=s.clientWidth,r=s.clientHeight,c=0,l=0;if(o){n=o.width,r=o.height;let a=Et();(!a||a&&e==="fixed")&&(c=o.offsetLeft,l=o.offsetTop)}return{width:n,height:r,x:c,y:l}}function Mo(t,e){let i=we(t,!0,e==="fixed"),s=i.top+t.clientTop,o=i.left+t.clientLeft,n=K(t)?Re(t):U(1),r=t.clientWidth*n.x,c=t.clientHeight*n.y,l=o*n.x,a=s*n.y;return{width:r,height:c,x:l,y:a}}function ps(t,e,i){let s;if(e==="viewport")s=Lo(t,i);else if(e==="document")s=Po(Y(t));else if(q(e))s=Mo(e,i);else{let o=ms(t);s={x:e.x-o.x,y:e.y-o.y,width:e.width,height:e.height}}return ge(s)}function ws(t,e){let i=te(t);return i===e||!q(i)||ve(i)?!1:$(i).position==="fixed"||ws(i,e)}function Do(t,e){let i=e.get(t);if(i)return i;let s=Me(t,[],!1).filter(c=>q(c)&&me(c)!=="body"),o=null,n=$(t).position==="fixed",r=n?te(t):t;for(;q(r)&&!ve(r);){let c=$(r),l=kt(r);!l&&c.position==="fixed"&&(o=null),(n?!l&&!o:!l&&c.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||De(r)&&!l&&ws(t,r))?s=s.filter(f=>f!==r):o=c,r=te(r)}return e.set(t,s),s}function Ro(t){let{element:e,boundary:i,rootBoundary:s,strategy:o}=t,r=[...i==="clippingAncestors"?Xe(e)?[]:Do(e,this._c):[].concat(i),s],c=r[0],l=r.reduce((a,f)=>{let d=ps(e,f,o);return a.top=W(d.top,a.top),a.right=ne(d.right,a.right),a.bottom=ne(d.bottom,a.bottom),a.left=W(d.left,a.left),a},ps(e,c,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function Fo(t){let{width:e,height:i}=gs(t);return{width:e,height:i}}function Io(t,e,i){let s=K(e),o=Y(e),n=i==="fixed",r=we(t,!0,n,e),c={scrollLeft:0,scrollTop:0},l=U(0);if(s||!s&&!n)if((me(e)!=="body"||De(o))&&(c=Ge(e)),s){let m=we(e,!0,n,e);l.x=m.x+e.clientLeft,l.y=m.y+e.clientTop}else o&&(l.x=ei(o));let a=o&&!s&&!n?vs(o,c):U(0),f=r.left+c.scrollLeft-l.x-a.x,d=r.top+c.scrollTop-l.y-a.y;return{x:f,y:d,width:r.width,height:r.height}}function Qt(t){return $(t).position==="static"}function bs(t,e){if(!K(t)||$(t).position==="fixed")return null;if(e)return e(t);let i=t.offsetParent;return Y(t)===i&&(i=i.ownerDocument.body),i}function ys(t,e){let i=H(t);if(Xe(t))return i;if(!K(t)){let o=te(t);for(;o&&!ve(o);){if(q(o)&&!Qt(o))return o;o=te(o)}return i}let s=bs(t,e);for(;s&&hs(s)&&Qt(s);)s=bs(s,e);return s&&ve(s)&&Qt(s)&&!kt(s)?i:s||ds(t)||i}var No=async function(t){let e=this.getOffsetParent||ys,i=this.getDimensions,s=await i(t.floating);return{reference:Io(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:s.width,height:s.height}}};function Wo(t){return $(t).direction==="rtl"}var Ho={convertOffsetParentRelativeRectToViewportRelativeRect:Oo,getDocumentElement:Y,getClippingRect:Ro,getOffsetParent:ys,getElementRects:No,getClientRects:To,getDimensions:Fo,getScale:Re,isElement:q,isRTL:Wo};function As(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function Bo(t,e){let i=null,s,o=Y(t);function n(){var c;clearTimeout(s),(c=i)==null||c.disconnect(),i=null}function r(c,l){c===void 0&&(c=!1),l===void 0&&(l=1),n();let a=t.getBoundingClientRect(),{left:f,top:d,width:m,height:g}=a;if(c||e(),!m||!g)return;let u=Ke(d),p=Ke(o.clientWidth-(f+m)),v=Ke(o.clientHeight-(d+g)),w=Ke(f),x={rootMargin:-u+"px "+-p+"px "+-v+"px "+-w+"px",threshold:W(0,ne(1,l))||1},O=!0;function T(le){let X=le[0].intersectionRatio;if(X!==l){if(!O)return r();X?r(!1,X):s=setTimeout(()=>{r(!1,1e-7)},1e3)}X===1&&!As(a,t.getBoundingClientRect())&&r(),O=!1}try{i=new IntersectionObserver(T,{...x,root:o.ownerDocument})}catch{i=new IntersectionObserver(T,x)}i.observe(t)}return r(!0),n}function xs(t,e,i,s){s===void 0&&(s={});let{ancestorScroll:o=!0,ancestorResize:n=!0,elementResize:r=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:l=!1}=s,a=Zt(t),f=o||n?[...a?Me(a):[],...Me(e)]:[];f.forEach(w=>{o&&w.addEventListener("scroll",i,{passive:!0}),n&&w.addEventListener("resize",i)});let d=a&&c?Bo(a,i):null,m=-1,g=null;r&&(g=new ResizeObserver(w=>{let[A]=w;A&&A.target===a&&g&&(g.unobserve(e),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var x;(x=g)==null||x.observe(e)})),i()}),a&&!l&&g.observe(a),g.observe(e));let u,p=l?we(t):null;l&&v();function v(){let w=we(t);p&&!As(p,w)&&i(),p=w,u=requestAnimationFrame(v)}return i(),()=>{var w;f.forEach(A=>{o&&A.removeEventListener("scroll",i),n&&A.removeEventListener("resize",i)}),d?.(),(w=g)==null||w.disconnect(),g=null,l&&cancelAnimationFrame(u)}}var _s=rs;var Ss=ls,ks=ns,Es=as;var Cs=(t,e,i)=>{let s=new Map,o={platform:Ho,...i},n={...o.platform,_c:s};return os(t,e,{...o,platform:n})};var z=class extends E{boot({options:e}){if(e({reference:null,auto:!0,position:"bottom start",gap:"5",offset:"0",matchWidth:!1,crossAxis:!1,scrollY:!0}),this.options().reference===null||this.options().position===null)return;let[i,s]=$o(this.el,{scrollY:this.options().scrollY}),o=Vo(this.el,this.options().reference,i,{position:this.options().position,gap:this.options().gap,offset:this.options().offset,matchWidth:this.options().matchWidth,crossAxis:this.options().crossAxis,scrollY:this.options().scrollY}),n=()=>{};this.reposition=(...r)=>{this.options().auto?n=xs(this.options().reference,this.el,o):o(null,...r)},this.cleanup=()=>{n(),s()}}};function Vo(t,e,i,{position:s,offset:o,gap:n,matchWidth:r,crossAxis:c,scrollY:l}){let a=window.getComputedStyle(t).maxHeight;return a=a==="none"?null:parseFloat(a),(f,d,m)=>{Cs(e,t,{placement:qo(s),middleware:[_s({mainAxis:Number(n),alignmentAxis:Number(o)}),ks(),Ss({padding:5,crossAxis:c}),Es({padding:5,apply({rects:g,elements:u,availableHeight:p}){r&&Object.assign(u.floating.style,{width:`${g.reference.width}px`});let v=a;v===null&&(v=l?u.floating.scrollHeight:u.floating.offsetHeight),u.floating.style.maxHeight=p>v?"":`${p}px`}})]}).then(({x:g,y:u})=>{i(d||g,m||u)})}}function qo(t){let e=t.split(" ");switch(e[0]){case"start":e[0]=ze()?"right":"left";break;case"end":e[0]=ze()?"left":"right";break}return e.join("-")}function $o(t,{scrollY:e=!0}){let i=(r,c)=>{Object.assign(t.style,{position:"absolute",overflowY:e?"auto":"hidden",left:`${r}px`,top:`${c}px`,right:"auto",bottom:"auto"})},s,o,n=new MutationObserver(()=>i(s,o));return[(r,c)=>{s=r,o=c,n.disconnect(),i(s,o),n.observe(t,{attributeFilter:["style"]})},()=>{n.disconnect()}]}var ti=class extends S{boot(){let e=this.trigger(),i=this.overlay();if(e){if(!i)return console.warn("ui-dropdown: no [popover] overlay found",this)}else return console.warn("ui-dropdown: no trigger element found",this);if(this._disabled=this.hasAttribute("disabled"),this._controllable=new L(this),i._popoverable=new V(i),i._anchorable=new z(i,{reference:e,position:this.hasAttribute("position")?this.getAttribute("position"):void 0,gap:this.hasAttribute("gap")?this.getAttribute("gap"):void 0,offset:this.hasAttribute("offset")?this.getAttribute("offset"):void 0}),i._popoverable.onChange(()=>{i._popoverable.getState()?i._anchorable.reposition():i._anchorable.cleanup()}),["ui-menu","ui-context"].includes(i.localName)){let{lock:n,unlock:r}=be(i._popoverable.el);i._popoverable.onChange(()=>{i._popoverable.getState()?n():r()})}this._controllable.initial(n=>i._popoverable.setState(n)),this._controllable.getter(()=>i._popoverable.getState());let s=P();this._controllable.setter(n=>i._popoverable.setState(n)),i._popoverable.onChange(s(()=>this._controllable.dispatch())),this.hasAttribute("hover")&&Te(e,i,{gain(){i._popoverable.setState(!0)},lose(){i._popoverable.setState(!1)},focusable:!1}),b(e,"click",()=>i._popoverable.toggle()),i._popoverable.getState()?(h(this,"data-open",""),h(e,"data-open",""),h(i,"data-open","")):(y(this,"data-open"),y(e,"data-open"),y(i,"data-open")),i._popoverable.onChange(()=>{i._popoverable.getState()?(h(this,"data-open",""),h(e,"data-open",""),h(i,"data-open","")):(y(this,"data-open"),y(e,"data-open"),y(i,"data-open"))});let o=F(i,"dropdown");h(e,"aria-haspopup","true"),h(e,"aria-controls",o),h(e,"aria-expanded",i._popoverable.getState()?"true":"false"),i._popoverable.onChange(()=>{h(e,"aria-expanded",i._popoverable.getState()?"true":"false")}),i._popoverable.onChange(()=>{setTimeout(()=>i._popoverable.getState()?i.onPopoverShow?.():i.onPopoverHide?.())})}trigger(){return this.querySelector("button")}overlay(){return this.lastElementChild?.matches("[popover]")&&this.lastElementChild}};_("dropdown",ti);var ii=class extends S{boot(){let e=this.hasAttribute("label")?"label":"description",i=this.button(),s=this.overlay();if(i){if(!s)return}else return console.warn("ui-tooltip: no trigger element found",this);this._disabled=this.hasAttribute("disabled"),s._popoverable=new V(s,{scope:"tooltip"}),s._anchorable=new z(s,{reference:i,position:this.hasAttribute("position")?this.getAttribute("position"):void 0,gap:this.hasAttribute("gap")?this.getAttribute("gap"):void 0,offset:this.hasAttribute("offset")?this.getAttribute("offset"):void 0}),s._popoverable.onChange(()=>{s._popoverable.getState()?s._anchorable.reposition():s._anchorable.cleanup()}),this._disabled||Te(i,s,{gain(){s._popoverable.setState(!0)},lose(){s._popoverable.setState(!1)},focusable:!0,useSafeArea:!1});let o=F(s,"tooltip"),n=this.hasAttribute("interactive"),r=this.hasAttribute("label")||i.textContent.trim()==="";n?(h(i,"aria-controls",o),h(i,"aria-expanded","false"),s._popoverable.onChange(()=>{s._popoverable.getState()?h(i,"aria-expanded","true"):h(i,"aria-expanded","false")})):(r?h(i,"aria-labelledby",o):h(i,"aria-describedby",o),h(s,"aria-hidden","true")),h(s,"role","tooltip")}button(){return this.firstElementChild}overlay(){return this.lastElementChild!==this.button()&&this.lastElementChild.tagName!=="TEMPLATE"&&this.lastElementChild}};_("tooltip",ii);var ye=class extends G{groupOfType=M;boot({options:e}){e({wrap:!1,ensureTabbable:!0})}mount(){this.options().ensureTabbable&&this.ensureTabbable()}focusFirst(){let e;e=e||this.walker().find(i=>i.hasAttribute("autofocus")),e=e||this.walker().find(i=>i.getAttribute("tabindex")==="0"),e=e||this.walker().find(i=>i.getAttribute("tabindex")==="-1"),e=e||this.walker().find(i=>Xi(i)),e?.focus()}focusPrev(){this.moveFocus(e=>this.options().wrap?this.walker().prevOrLast(e):this.walker().prev(e))}focusNext(){this.moveFocus(e=>this.options().wrap?this.walker().nextOrFirst(e):this.walker().next(e))}focusBySearch(e){let i=this.walker().find(s=>s.textContent.toLowerCase().trim().startsWith(e.toLowerCase()));i?.use(M).tabbable(),i?.use(M).focus()}moveFocus(e){let i=this.walker().find(o=>o.use(M).isTabbable());e(i)?.use(M).focus()}ensureTabbable(){this.walker().findOrFirst(e=>{e.use(M).isTabbable()})?.use(M).tabbable()}wipeTabbables(){this.walker().each(e=>{e.use(M).untabbable()})}untabbleOthers(e){this.walker().each(i=>{i!==e&&i.use(M).untabbable()})}walker(){return R(this.el,(e,{skip:i,reject:s})=>{if(e[this.constructor.name]&&e!==this.el)return s();if(!e[this.groupOfType.name])return i();if(e.hasAttribute("disabled"))return s()})}},M=class extends E{groupedByType=ye;boot({options:e}){e({hover:!1,disableable:null,tabbable:!1,tabbableAttr:null})}mount(){let e=this.options().disableable;if(!e)throw"Focusable requires a Disableable instance...";this.el.hasAttribute("tabindex")||(this.options().tabbable?this.tabbable():this.untabbable()),this.pauseFocusListener=this.on("focus",e.enabled(()=>{this.focus(!1)})).pause,this.on("focus",e.enabled(()=>{$e()&&h(this.el,"data-focus","")})),this.on("blur",e.enabled(()=>{y(this.el,"data-focus")})),this.options().hover&&this.on("pointerenter",e.enabled(()=>{this.group()?.untabbleOthers(this.el),this.tabbable()})),this.options().hover&&this.on("pointerleave",e.enabled(i=>{this.untabbable()}))}focus(e=!0){this.group()?.untabbleOthers(this.el),this.tabbable(),e&&this.pauseFocusListener(()=>{this.el.focus({focusVisible:!1})})}tabbable(){h(this.el,"tabindex","0"),this.options().tabbableAttr&&h(this.el,this.options().tabbableAttr,"")}untabbable(){h(this.el,"tabindex","-1"),this.options().tabbableAttr&&y(this.el,this.options().tabbableAttr)}isTabbable(){return this.el.getAttribute("tabindex")==="0"}};var si=class extends I{boot(){let e=this;this._disableable=new N(this),this._selectable=new k(e,{toggleable:!0,dataAttr:"data-checked",ariaAttr:"aria-checked",value:this.hasAttribute("value")?this.getAttribute("value"):null,label:this.hasAttribute("label")?this.getAttribute("label"):null,selectedInitially:this.hasAttribute("checked")}),this._submittable=new j(this,{name:this.getAttribute("name"),value:this.getAttribute("value")??"on",includeWhenEmpty:!1,shouldUpdateValue:!1}),this.value=this._selectable.getValue(),this._detangled=P(),this._selectable.onChange(this._detangled(()=>{this.dispatchEvent(new Event("input",{bubbles:!1,cancelable:!0})),this.dispatchEvent(new Event("change",{bubbles:!1,cancelable:!0}))})),h(e,"role","switch"),this._selectable.onInitAndChange(()=>{this._submittable.update(this._selectable.getState())}),this._disableable.onInitAndChange(i=>{i?y(e,"tabindex","0"):h(e,"tabindex","0")}),b(e,"click",this._disableable.disabled(i=>{i.preventDefault(),i.stopPropagation()}),{capture:!0}),b(e,"click",this._disableable.enabled(i=>{this._selectable.press()})),b(e,"keydown",this._disableable.enabled(i=>{i.key==="Enter"&&(this._selectable.press(),i.preventDefault(),i.stopPropagation())})),b(e,"keydown",this._disableable.enabled(i=>{i.key===" "&&(i.preventDefault(),i.stopPropagation())})),b(e,"keyup",this._disableable.enabled(i=>{i.key===" "&&(this._selectable.press(),i.preventDefault(),i.stopPropagation())})),zo(e)}get checked(){return this._selectable.isSelected()}set checked(e){this._detangled(()=>{e?this._selectable.select():this._selectable.deselect()})()}};function zo(t){t.closest("label")?.addEventListener("click",e=>{t.contains(e.target)||t.click()})}C(({css:t})=>t`ui-switch { display: inline-block; user-select: none; }`);_("switch",si);var oi=class t extends S{mount(){this.control=this.fieldWalker().find(e=>this.isControl(e)),this.control}associateLabelWithControl(e){e&&this.label&&(this.control=e,!this.control.hasAttribute("aria-labelledby")&&(h(this.elOrButton(this.control),"aria-labelledby",this.label.id),this.control&&!(this.control instanceof I)&&this.hasAttribute("disabled")&&this.control.setAttribute("disabled","")))}associateDescriptionWithControl(e){e&&this.description&&(this.control=e,!this.control.hasAttribute("aria-describedby")&&h(this.elOrButton(this.control),"aria-describedby",this.description.id))}associateLabel(e){this.label=e,b(e,"click",i=>{["a","button"].includes(i.target.localName)||this.focusOrTogggle(this.control)}),this.control&&this.associateLabelWithControl(this.control)}associateDescription(e){this.description=e,this.control&&this.associateDescriptionWithControl(this.control)}fieldWalker(){return R(this,(e,{skip:i,reject:s})=>{if(e instanceof t&&e!==this||e.parentElement.localName==="ui-editor"&&e!==this)return s()})}isControl(e){return!!(e instanceof I||e.matches("input, textarea, select"))}focusOrTogggle(e){if(!e||e.disabled||e.hasAttribute("disabled"))return;e.localName==="input"&&["checkbox","radio"].includes(e.type)||["ui-switch","ui-radio","ui-checkbox"].includes(e.localName)?(e.click(),e.focus()):e.localName==="input"&&["file"].includes(e.type)?e.click():["ui-select"].includes(e.localName)?e.trigger().focus():(["ui-editor"].includes(e.localName),e.focus())}elOrButton(e){return e instanceof S&&e.firstElementChild instanceof HTMLButtonElement?e.firstElementChild:e}},ni=class extends S{mount(){F(this,"label"),h(this,"aria-hidden","true"),this.closest("ui-field")?.associateLabel(this)}},ri=class extends S{mount(){F(this,"description"),h(this,"aria-hidden","true"),this.closest("ui-field")?.associateDescription(this)}};C(({css:t})=>t`
    ui-label { display: inline-block; cursor: default; }
    ui-description { display: block; }
`);_("field",oi);_("label",ni);_("description",ri);var li=class extends I{boot(){this._selectable=new B(this),this._controllable=new L(this,{disabled:this._disabled,bubbles:!0}),this._focusable=new ye(this,{wrap:!0}),this._submittable=new j(this,{name:this.getAttribute("name"),value:this._selectable.getState(),includeWhenEmpty:!1}),this._controllable.initial(e=>e&&this._selectable.setState(e)),this._controllable.getter(()=>this._selectable.getState()),this._detangled=P(),this._controllable.setter(this._detangled(e=>{this._selectable.setState(e)})),this._selectable.onChange(this._detangled(()=>{this._controllable.dispatch()})),this._selectable.onInitAndChange(()=>{this._submittable.update(this._selectable.getState())}),b(this,"keydown",e=>{["ArrowDown","ArrowRight"].includes(e.key)?(this._focusable.focusNext(),e.preventDefault(),e.stopPropagation()):["ArrowUp","ArrowLeft"].includes(e.key)&&(this._focusable.focusPrev(),e.preventDefault(),e.stopPropagation())}),h(this,"role","radiogroup"),queueMicrotask(()=>{this._submittable.update(this._selectable.getState())})}},ai=class extends I{boot(){let e=this;this._disableable=new N(this),this._selectable=new k(e,{value:this.hasAttribute("value")?this.getAttribute("value"):Math.random().toString(36).substring(2,10),label:this.hasAttribute("label")?this.getAttribute("label"):null,selectedInitially:this.hasAttribute("checked"),dataAttr:"data-checked",ariaAttr:"aria-checked"}),this.value=this._selectable.getValue(),this._selectable.onChange(()=>{this._selectable.isSelected()&&this._focusable.focus(!1)}),this._disableable.onChange(i=>{i?this._focusable.untabbable():this._selectable.isSelected()&&this._focusable.tabbable()}),h(e,"role","radio"),this._focusable=new M(e,{disableable:this._disableable,tabbableAttr:"data-active"}),b(e,"click",this._disableable.disabled(i=>{i.preventDefault(),i.stopPropagation()}),{capture:!0}),b(e,"click",this._disableable.enabled(i=>{this._selectable.press()})),b(e,"keydown",this._disableable.enabled(i=>{i.key==="Enter"&&(this._selectable.press(),i.preventDefault(),i.stopPropagation())})),b(e,"keydown",this._disableable.enabled(i=>{i.key===" "&&(i.preventDefault(),i.stopPropagation())})),b(e,"keyup",this._disableable.enabled(i=>{i.key===" "&&(this._selectable.press(),i.preventDefault(),i.stopPropagation())})),jo(e),b(e,"focus",i=>{$e()&&this._selectable.select()})}get checked(){return this._selectable.isSelected()}set checked(e){(this.closest("ui-radio-group")?._detangled||(()=>{}))(()=>{e&&this._selectable.select()})()}};function jo(t){t.closest("label")?.addEventListener("click",e=>{t.contains(e.target)||t.click()})}C(({css:t})=>t`ui-radio-group { display: block; }`);C(({css:t})=>t`ui-radio { display: inline-block; user-select: none; }`);_("radio-group",li);_("radio",ai);var Je=class extends G{groupOfType=D;boot({options:e}){e({wrap:!1,filter:!1}),this.onChanges=[]}onChange(e){this.onChanges.push(e)}activated(e){this.onChanges.forEach(i=>i())}activateFirst(){this.filterAwareWalker().first()?.use(D).activate()}activateBySearch(e){this.filterAwareWalker().find(s=>s.textContent.toLowerCase().trim().startsWith(e.toLowerCase()))?.use(D).activate()}activateSelectedOrFirst(e){if(!e||(s=>s.matches("ui-option")?getComputedStyle(s).display==="none":!1)(e)){this.filterAwareWalker().first()?.use(D).activate();return}e?.use(D).activate()}activateActiveOrFirst(){let e=this.getActive();if(!e){this.filterAwareWalker().first()?.use(D).activate();return}e?.use(D).activate()}activateActiveOrLast(){let e=this.getActive();if(!e){this.filterAwareWalker().last()?.use(D).activate();return}e?.use(D).activate()}activatePrev(){let e=this.getActive();if(!e){this.filterAwareWalker().last()?.use(D).activate();return}let i;this.options.wrap?i=this.filterAwareWalker().prevOrLast(e):i=this.filterAwareWalker().prev(e),i?.use(D).activate()}activateNext(){let e=this.getActive();if(!e){this.filterAwareWalker().first()?.use(D).activate();return}let i;this.options.wrap?i=this.filterAwareWalker().nextOrFirst(e):i=this.filterAwareWalker().next(e),i?.use(D).activate()}getActive(){return this.walker().find(e=>e.use(D).isActive())}clearActive(){this.getActive()?.use(D).deactivate()}filterAwareWalker(){let e=i=>i.matches("ui-option")?getComputedStyle(i).display==="none":!1;return R(this.el,(i,{skip:s,reject:o})=>{if(i[this.constructor.name]&&i!==this.el)return o();if(!i[this.groupOfType.name])return s();if(i.hasAttribute("disabled")||e(i))return o()})}},D=class t extends E{groupedByType=Je;mount(){this.el.addEventListener("mouseenter",()=>{this.activate()}),this.el.addEventListener("mouseleave",()=>{this.deactivate()})}activate(){this.group()&&this.group().walker().each(e=>e.use(t).deactivate(!1)),!this.el.hasAttribute("disabled")&&(h(this.el,"data-active",""),this.el.scrollIntoView({block:"nearest"}),this.group()&&this.group().activated(this.el))}deactivate(e=!0){y(this.el,"data-active"),e&&this.group()&&this.group().activated(this.el)}isActive(){return this.el.hasAttribute("data-active")}};var Qe=class extends G{groupOfType=Ae;boot({options:e}){e({}),this.onChanges=[],this.lastSearch=""}onChange(e){this.onChanges.push(e)}filter(e){e===""?this.walker().each(i=>{i.use(Ae).unfilter()}):this.walker().each(i=>{this.matches(i,e)?i.use(Ae).unfilter():i.use(Ae).filter()}),this.lastSearch!==e&&this.onChanges.forEach(i=>i()),this.lastSearch=e}matches(e,i){return e.textContent.toLowerCase().trim().includes(i.toLowerCase().trim())}hasResults(){return this.walker().some(e=>!e.use(Ae).isFiltered())}},Ae=class extends E{groupedByType=Qe;boot({options:e}){e({mirror:null,keep:!1}),this.onChanges=[]}filter(){this.options().keep||(h(this.el,"data-hidden",""),this.options().mirror&&h(this.options().mirror,"data-hidden",""))}unfilter(){y(this.el,"data-hidden"),this.options().mirror&&y(this.options().mirror,"data-hidden","")}isFiltered(){return this.el.hasAttribute("data-hidden")}};var Ot=class extends S{boot(){if(this.querySelectorAll("[data-appended]").forEach(e=>e.remove()),!this.querySelector("template")){let e=document.createElement("template");e.setAttribute("name","placeholder"),e.innerHTML="<span>"+this.innerHTML+"</span>",this.innerHTML="",this.appendChild(e)}if(!this.querySelector('template[name="options"]')){let e=document.createElement("template");e.setAttribute("name","options"),e.innerHTML="<div><slot></slot></div>",this.appendChild(e)}if(!this.querySelector('template[name="option"]')){let e=document.createElement("template");e.setAttribute("name","option"),e.innerHTML="<div><slot></slot></div>",this.appendChild(e)}this.templates={placeholder:this.querySelector('template[name="placeholder"]'),overflow:this.querySelector('template[name="overflow"]'),options:this.querySelector('template[name="options"]'),option:this.querySelector('template[name="option"]')},this.templates.options.elsByValue=new Map,this.max=this.templates.overflow?.getAttribute("max")?this.templates.overflow.getAttribute("max"):1/0,this.selecteds=new Map,this.picker=this.closest("ui-select"),this.multiple=this.picker.hasAttribute("multiple")}mount(){queueMicrotask(()=>{this.picker._selectable.onInitAndChange(()=>{this.render(!0)});let e=this.picker.list();e&&new MutationObserver(i=>{queueMicrotask(()=>this.render())}).observe(e,{childList:!0})})}render(e){if(this.multiple){let i=this.picker.value,s=Array.from(this.selecteds.keys()).filter(r=>!i.includes(r)),o=i.filter(r=>!this.selecteds.has(r));s.forEach(r=>this.selecteds.delete(r));let n=new Map;for(let r of o){let c=this.picker._selectable.findByValue(r);if(!c){if(e)return setTimeout(()=>this.render());throw`Could not find option for value "${r}"`}n.set(r,c)}n.forEach((r,c)=>this.selecteds.set(c,r)),this.templates.placeholder?.clearPlaceholder?.(),this.templates.overflow?.clearOverflow?.(),this.templates.options?.clearOptions?.(),this.selecteds.size>0?this.renderOptions({hasOverflowed:r=>{if(this.max==="auto"){let c=!1;if(this.renderOverflow(this.selecteds.size,this.selecteds.size-r),this.clientWidth<this.scrollWidth&&(c=!0),this.templates.overflow?.clearOverflow?.(),c)return!0}return r>parseInt(this.max)},renderOverflow:r=>{this.templates?.overflow?.getAttribute("mode")!=="append"&&this.templates.options?.clearOptions?.(),this.renderOverflow(this.selecteds.size,r)}}):this.renderPlaceholder()}else{let i=this.picker.value;if(Array.from(this.selecteds.keys()).includes(i))return;this.selecteds.clear();let s=this.picker._selectable.findByValue(i);if(s)this.selecteds.set(i,s);else if(!["",null,void 0].includes(i)){if(e)return setTimeout(()=>{console.log("retrying..."),this.render()});throw`Could not find option for value "${i}"`}this.templates.placeholder?.clearPlaceholder?.(),this.templates.option?.clearOption?.(),this.selecteds.size>0?this.renderOption():this.renderPlaceholder()}}renderOptions({hasOverflowed:e,renderOverflow:i}){let s=document.createElement("div");s.style.display="contents";let o=Ze(this.templates.options,{default:s});this.templates.options.after(o),this.templates.options.clearOptions=()=>{o.remove(),this.templates.options.clearOptions=()=>{}};let n=0,r=!1;for(let[l,a]of this.selecteds){let f=new DocumentFragment;f.append(...a.el.cloneNode(!0).childNodes);let d=Ze(this.templates.option,{text:a.el.textContent.trim(),default:f,value:l});if(d.setAttribute("data-value",l),d.setAttribute("data-appended",""),d.deselect=()=>a.deselect(),s.appendChild(d),n++,e(n)){r=!0,s.removeChild(d),n--;break}}let c=new DocumentFragment;c.append(...s.childNodes),s.replaceWith(c),r&&i(this.selecteds.size-n)}renderOption(){for(let[e,i]of this.selecteds){let s=new DocumentFragment;s.append(...i.el.cloneNode(!0).childNodes);let o=Ze(this.templates.option,{text:i.el.textContent.trim(),default:s,value:e});o.setAttribute("data-value",e),o.setAttribute("data-appended",e),o.deselect=()=>i.deselect(),this.templates.option.after(o),this.templates.option.clearOption=()=>{o.remove(),this.templates.option.clearOption=()=>{}}}}renderPlaceholder(){if(!this.templates.placeholder)return;let e=Ze(this.templates.placeholder);e.setAttribute("data-appended",""),this.templates.placeholder.after(e),this.templates.placeholder.clearPlaceholder=()=>{e.remove(),this.templates.placeholder.clearPlaceholder=()=>{}}}renderOverflow(e,i){if(!this.templates.overflow)return;let s=Ze(this.templates.overflow,{remainder:i,count:this.selecteds.size});s.setAttribute("data-appended",""),this.templates.overflow.after(s),this.templates.overflow.clearOverflow=()=>{s.remove(),this.templates.placeholder.clearOverflow=()=>{}}}};function Ze(t,e={}){let i=t.content.cloneNode(!0);return Object.entries(e).forEach(([s,o])=>{(s==="default"?i.querySelectorAll("slot:not([name])"):i.querySelectorAll(`slot[name="${s}"]`)).forEach(r=>r.replaceWith(typeof o=="string"?document.createTextNode(o):o))}),i.firstElementChild}var it=class extends I{boot(){let e=this.list();this._controllable=new L(this,{bubbles:!0}),this._selectable=new B(e,{multiple:this.hasAttribute("multiple")}),this._submittable=new j(this,{name:this.getAttribute("name"),value:this._selectable.getState()}),this._controllable.initial(s=>s&&this._selectable.setState(s)),this._controllable.getter(()=>this._selectable.getState());let i=P();this._controllable.setter(i(s=>{this._selectable.setState(s)})),this._selectable.onChange(i(()=>{this._controllable.dispatch(),this.dispatchEvent(new CustomEvent("select",{bubbles:!1}))})),this._selectable.onInitAndChange(()=>{this._submittable.update(this._selectable.getState())}),queueMicrotask(()=>{this._submittable.update(this._selectable.getState())})}mount(){this._disableable=new N(this);let e=this.input(),i=this.button(),s=this.list(),o=this.hasAttribute("multiple"),n=this.hasAttribute("autocomplete"),r=this.hasAttribute("autocomplete")&&this.getAttribute("autocomplete").trim().split(" ").includes("strict"),c=this.querySelector("ui-options")||this,l=Uo(c,"options");this._activatable=new Je(c,{filter:"data-hidden"}),!e&&!i&&this._disableable.onInitAndChange(u=>{u?this.removeAttribute("tabindex"):this.setAttribute("tabindex","0")}),this.hasAttribute("filter")&&this.getAttribute("filter")!=="manual"&&(this._filterable=new Qe(s),this._filterable.onChange(()=>{this._activatable.clearActive(),this._filterable.hasResults()&&this._activatable.activateFirst()}),this.addEventListener("close",()=>{this._filterable&&this._filterable.filter("")}));let a=this.querySelector("[popover]:not(ui-tooltip > [popover])"),f=a?.querySelector('input:not([type="hidden"])'),d=this.querySelector('input:not([type="hidden"])');d=a?.contains(d)?null:d;let m=this.querySelector("button");if(m=a?.contains(m)?null:m,!(a||d))et(this,this._activatable),tt(this,this,this._activatable),Ts(this,this._activatable,this._selectable);else if(!a&&d){let u=d;this._disableable.onInitAndChange(p=>{p?u&&h(u,"disabled",""):u&&y(u,"disabled")}),gi(this,u,this._selectable,this._popoverable),Ts(u,this._activatable,this._selectable),Ls(n,r,this,u,this._selectable,this._popoverable),bi(u),pi(u),this._filterable&&fi(u,this._filterable),vi(u,this._activatable),et(u,this._activatable),tt(this,u,this._activatable),Tt(this,this._activatable)}else if(a&&d){let u=d;h(u,"role","combobox"),h(u,"aria-controls",l);let p=a;this._popoverable=new V(p),this._anchorable=new z(p,{reference:u,matchWidth:!0,position:this.hasAttribute("position")?this.getAttribute("position"):void 0,gap:this.hasAttribute("gap")?this.getAttribute("gap"):void 0,offset:this.hasAttribute("offset")?this.getAttribute("offset"):void 0}),Ls(n,r,this,u,this._selectable,this._popoverable),this._disableable.onInitAndChange(v=>{v?u&&h(u,"disabled",""):u&&y(u,"disabled")}),this.querySelectorAll("button").forEach(v=>{p.contains(v)||(h(v,"tabindex","-1"),h(v,"aria-controls",l),h(v,"aria-haspopup","listbox"),Pt(v,this._popoverable),b(v,"click",()=>{this._popoverable.toggle(),u.focus()}))}),gi(this,u,this._selectable,this._popoverable),ci(this,u,p,this._popoverable,this._anchorable),wi(this,this._popoverable),Pt(u,this._popoverable),bi(u),pi(u),this._filterable&&fi(u,this._filterable),vi(u,this._activatable),Xo(u,this._popoverable),di(u,this._popoverable,this._activatable,this._selectable),Yo(u,this._popoverable),hi(this,this._popoverable),et(u,this._activatable),tt(this,u,this._activatable),Tt(this,this._activatable),ui(this._popoverable,this._activatable,this._selectable),mi(this,this._selectable,this._popoverable,o)}else if(a&&f){let u=m,p=f,v=a;h(u,"role","combobox"),h(p,"role","combobox"),h(u,"aria-controls",l),this._disableable.onInitAndChange(w=>{w?(u&&h(u,"disabled",""),p&&h(p,"disabled","")):(u&&y(u,"disabled"),p&&y(p,"disabled"))}),this._popoverable=new V(v),this._anchorable=new z(v,{reference:u,matchWidth:!0,position:this.hasAttribute("position")?this.getAttribute("position"):void 0,gap:this.hasAttribute("gap")?this.getAttribute("gap"):void 0,offset:this.hasAttribute("offset")?this.getAttribute("offset"):void 0,scrollY:!1}),bi(p),pi(p),this._filterable&&fi(p,this._filterable),Ko(p,this._popoverable),ci(this,u,v,this._popoverable,this._anchorable),wi(this,this._popoverable),Pt(u,this._popoverable),gi(this,p,this._selectable,this._popoverable),di(u,this._popoverable,this._activatable,this._selectable),Ps(u,this._popoverable),hi(this,this._popoverable),et(p,this._activatable),Os(u,this._activatable,this._popoverable),tt(this,p,this._activatable),Tt(this,this._activatable),ui(this._popoverable,this._activatable,this._selectable),mi(this,this._selectable,this._popoverable,o)}else if(a){let u=m,p=a;h(u,"role","combobox"),h(u,"aria-controls",l),this._disableable.onInitAndChange(v=>{v?(u&&h(u,"disabled",""),e&&h(e,"disabled","")):(u&&y(u,"disabled"),e&&y(e,"disabled"))}),this._popoverable=new V(p),this._anchorable=new z(p,{reference:u,matchWidth:!0,position:this.hasAttribute("position")?this.getAttribute("position"):void 0,gap:this.hasAttribute("gap")?this.getAttribute("gap"):void 0,offset:this.hasAttribute("offset")?this.getAttribute("offset"):void 0}),ci(this,u,p,this._popoverable,this._anchorable),wi(this,this._popoverable),Pt(u,this._popoverable),di(u,this._popoverable,this._activatable,this._selectable),Ps(u,this._popoverable),vi(u,this._activatable),hi(this,this._popoverable),et(u,this._activatable),Os(u,this._activatable,this._popoverable),tt(this,u,this._activatable),Tt(this,this._activatable),ui(this._popoverable,this._activatable,this._selectable),mi(this,this._selectable,this._popoverable,o)}new MutationObserver(()=>{setTimeout(()=>{if(!this._popoverable||this._popoverable.getState()){let u=this._selectable.selecteds().find(p=>!p.el._disableable.isDisabled())?.el;setTimeout(()=>{this._activatable.activateSelectedOrFirst(u)})}else this._activatable.clearActive()})}).observe(s,{childList:!0})}button(){return Array.from(this.querySelectorAll("button")).find(e=>e.nextElementSibling?.matches("[popover]"))||null}input(){return this.querySelector('input:not([type="hidden"])')}list(){return this.querySelector("ui-options")||this}clear(){this.input()&&(this.input().value="",this.input().dispatchEvent(new Event("input",{bubbles:!1})))}open(){this._popoverable.setState(!0)}close(){this._popoverable.setState(!1)}deselectLast(){!this.hasAttribute("multiple")&&this.value!==null&&(this.value=null,this.dispatchEvent(new Event("input",{bubbles:!1})),this.dispatchEvent(new Event("change",{bubbles:!1}))),this.hasAttribute("multiple")&&this.value.length!==0&&(this.value=this.value.slice(0,-1),this.dispatchEvent(new Event("input",{bubbles:!1})),this.dispatchEvent(new Event("change",{bubbles:!1})))}},yi=class extends S{boot(){h(this,"data-hidden","")}mount(){queueMicrotask(()=>{let e=this.closest("ui-autocomplete, ui-combobox, ui-select"),i=this.closest("ui-options");if(!i)return;let s=c=>c.hasAttribute("data-hidden"),o=()=>{let c;CSS.supports("selector(&)")?c=Array.from(i.querySelectorAll("& > ui-option")).filter(l=>!s(l)).length===0:c=Array.from(i.querySelectorAll(":scope > ui-option")).filter(l=>!s(l)).length===0,c?y(this,"data-hidden"):h(this,"data-hidden","")};o();let n=e._filterable;n&&n.onChange(o),new MutationObserver(c=>{setTimeout(()=>o())}).observe(i,{childList:!0})})}};_("selected",Ot);_("select",it);_("empty",yi);C(({css:t})=>t`ui-select { display: block; }`);C(({css:t})=>t`ui-selected-option { display: contents; }`);C(({css:t})=>t`ui-empty { display: block; cursor: default; }`);function et(t,e){b(t,"keydown",i=>{["ArrowDown","ArrowUp"].includes(i.key)&&(i.key==="ArrowDown"?(e.activateNext(),i.preventDefault(),i.stopPropagation()):i.key==="ArrowUp"&&(e.activatePrev(),i.preventDefault(),i.stopPropagation()))})}function Os(t,e,i){wt(t,s=>{e.activateBySearch(s),i.getState()||e.getActive()?.click()})}function tt(t,e,i){b(e,"keydown",s=>{if(s.key==="Enter"){let o=i.getActive();if(s.preventDefault(),s.stopPropagation(),!o)return;o.click(),t.dispatchEvent(new CustomEvent("action",{bubbles:!1,cancelable:!1}))}})}function Tt(t,e,i=!1){b(t,i?"pointerdown":"click",s=>{if(s.target.closest("ui-option")){let o=s.target.closest("ui-option");if(o._disableable.isDisabled())return;o._selectable?.trigger(),t.dispatchEvent(new CustomEvent("action",{bubbles:!1,cancelable:!1})),s.preventDefault(),s.stopPropagation()}})}function Ts(t,e,i){b(t,"focus",()=>{let s=i.selecteds().find(o=>!o.el._disableable.isDisabled())?.el;e.activateSelectedOrFirst(s)}),b(t,"blur",()=>{e.clearActive()})}function Uo(t){let e=F(t,"options");return h(t,"role","listbox"),e}function Pt(t,e){h(t,"aria-haspopup","listbox");let i=()=>{h(t,"aria-expanded",e.getState()?"true":"false"),e.getState()?h(t,"data-open",""):y(t,"data-open","")};e.onChange(()=>{i()}),i()}function ci(t,e,i,s,o){let n=()=>{Array.from([t,i]).forEach(r=>{s.getState()?h(r,"data-open",""):y(r,"data-open","")}),s.getState()?o.reposition():o.cleanup()};s.onChange(()=>n()),n(),s.onChange(()=>{s.getState()?t.dispatchEvent(new Event("open",{bubbles:!1,cancelable:!1})):t.dispatchEvent(new Event("close",{bubbles:!1,cancelable:!1}))})}function ui(t,e,i){t.onChange(()=>{if(t.getState()){let s=i.selecteds().find(o=>!o.el._disableable.isDisabled())?.el;setTimeout(()=>{e.activateSelectedOrFirst(s)})}else e.clearActive()})}function hi(t,e){b(t,"keydown",i=>{i.key==="Escape"&&e.getState()&&(e.setState(!1),i.preventDefault(),i.stopImmediatePropagation())})}function di(t,e){b(t,"keydown",i=>{["ArrowDown","ArrowUp"].includes(i.key)&&(i.key==="ArrowDown"||i.key==="ArrowUp")&&(e.getState()||(e.setState(!0),i.preventDefault(),i.stopImmediatePropagation()))})}function Yo(t,e){b(t,"click",()=>{e.getState()||(e.setState(!0),t.focus())})}function Ps(t,e){b(t,"click",()=>{e.setState(!e.getState()),t.focus()})}function Ko(t,e){e.onChange(()=>{e.getState()&&setTimeout(()=>t.focus())})}function fi(t,e){e&&b(t,"input",i=>{e.filter(i.target.value)})}function pi(t){b(t,"focus",()=>t.select())}function bi(t){b(t,"change",e=>e.stopPropagation()),b(t,"input",e=>e.stopPropagation())}function Xo(t,e){b(t,"keydown",i=>{(/^[a-zA-Z0-9]$/.test(i.key)||i.key==="Backspace")&&(e.getState()||e.setState(!0))})}function gi(t,e,i,s){if(!t.hasAttribute("clear"))return;let n=d=>{e.value=d,e.dispatchEvent(new Event("input",{bubbles:!1}))},r=t.getAttribute("clear"),c=r===""||r.split(" ").includes("action"),l=r===""||r.split(" ").includes("select"),a=r===""||r.split(" ").includes("close"),f=r===""||r.split(" ").includes("esc");r==="none"&&(c=l=a=f=!1),c?t.addEventListener("action",d=>{n("")}):l&&i.onChange(()=>{queueMicrotask(()=>n(""))}),a&&s.onChange(()=>{s.getState()||n("")}),f&&b(e,"keydown",d=>{d.key==="Escape"&&n("")})}function mi(t,e,i,s){let o=!s,n=!s;if(t.hasAttribute("close")){let r=t.getAttribute("close");o=r===""||r.split(" ").includes("action"),n=r.split(" ").includes("select"),r==="none"&&(o=n=!1)}o?t.addEventListener("action",r=>{i.setState(!1)}):n&&e.onChange(()=>{i.setState(!1)})}function vi(t,e){e.onChange(()=>{let i=e.getActive();i?h(t,"aria-activedescendant",i.id):y(t,"aria-activedescendant")})}function Ls(t,e,i,s,o,n){if(!t){h(s,"autocomplete","off"),h(s,"aria-autocomplete","none");return}let r=c=>{s.value=c,s.dispatchEvent(new Event("input",{bubbles:!1}))};h(s,"autocomplete","off"),h(s,"aria-autocomplete","list"),o.setState(s.value),queueMicrotask(()=>{o.onInitAndChange(()=>{s.value=o.selectedTextValue()})}),i.addEventListener("action",c=>{r(o.selectedTextValue())}),e&&n.onChange(()=>{n.getState()||r(o.selectedTextValue())})}function wi(t,e){let{lock:i,unlock:s}=be(e.el);e.onChange(()=>{e.getState()?i():s()})}var Ai=class t extends S{boot(){if(this._focusable=new ye(this,{wrap:!1,ensureTabbable:!1}),b(this,"keydown",e=>{["ArrowDown"].includes(e.key)?(e.target===this?this._focusable.focusFirst():this._focusable.focusNext(),e.preventDefault(),e.stopPropagation()):["ArrowUp"].includes(e.key)&&(e.target===this?this._focusable.focusFirst():this._focusable.focusPrev(),e.preventDefault(),e.stopPropagation())}),wt(this,e=>this._focusable.focusBySearch(e)),this.hasAttribute("popover")&&this.addEventListener("lofi-close-popovers",()=>{this.hasAttribute("keep-open")||setTimeout(()=>this.hidePopover(),50)}),this.parentElement.localName==="ui-dropdown"){let e=this.parentElement;b(e.trigger(),"keydown",i=>{i.key==="ArrowDown"&&(this.fromArrowDown=!0,this.showPopover(),i.preventDefault(),i.stopPropagation())})}h(this,"role","menu"),h(this,"tabindex","-1")}mount(){this.initializeMenuItems(),new MutationObserver(i=>{this.initializeMenuItems()}).observe(this,{childList:!0,subtree:!0})}onPopoverShow(){queueMicrotask(()=>{this.fromArrowDown?(this._focusable.focusFirst(),this.fromArrowDown=!1):this.focus()})}onPopoverHide(){this._focusable.wipeTabbables()}initializeMenuItems(){this.walker().each(e=>{e._disableable||Go(e)})}walker(){return R(this,(e,{skip:i,reject:s})=>{if(e instanceof t||e instanceof it)return s();if(!["a","button"].includes(e.localName))return i()})}},xi=class extends S{boot(){}},_i=class extends S{boot(){this._disabled=this.hasAttribute("disabled"),this._disableable=new N(this);let e=this;if(this._disabled&&(h(e,"disabled",""),h(e,"aria-disabled","true")),F(e,"menu-checkbox"),h(e,"role","menuitemcheckbox"),this._disabled)return;e._focusable=new M(e,{disableable:this._disableable,hover:!0,tabbableAttr:"data-active"}),e._selectable=new k(e,{toggleable:!0,value:this.hasAttribute("value")?this.getAttribute("value"):e.textContent.trim(),label:this.hasAttribute("label")?this.getAttribute("label"):e.textContent.trim(),dataAttr:"data-checked",ariaAttr:"aria-checked",selectedInitially:this.hasAttribute("checked")}),this._controllable=new L(this),this._controllable.initial(s=>s&&e._selectable.setState(s)),this._controllable.getter(()=>e._selectable.getState());let i=P();this._controllable.setter(i(s=>{this._selectable.setState(s)})),this._selectable.onChange(i(()=>{this._controllable.dispatch()})),b(e,"click",()=>{this.hasAttribute("keep-open")||this.dispatchEvent(new CustomEvent("lofi-close-popovers",{bubbles:!0})),e._selectable.press()}),Ci(e)}},Si=class extends S{boot(){this._disabled=this.hasAttribute("disabled"),this._disableable=new N(this);let e=this;this._disabled&&(h(e,"disabled",""),h(e,"aria-disabled","true")),F(e,"menu-radio"),h(e,"role","menuitemradio"),!this._disabled&&(e._focusable=new M(e,{disableable:this._disableable,hover:!0,tabbableAttr:"data-active"}),e._selectable=new k(e,{toggleable:!1,value:this.hasAttribute("value")?this.getAttribute("value"):e.textContent.trim(),label:this.hasAttribute("label")?this.getAttribute("label"):e.textContent.trim(),dataAttr:"data-checked",ariaAttr:"aria-checked",selectedInitially:this.hasAttribute("checked")}),b(e,"click",()=>{this.hasAttribute("keep-open")||this.dispatchEvent(new CustomEvent("lofi-close-popovers",{bubbles:!0})),e._selectable.press()}),Ci(e))}},ki=class extends S{boot(){this._selectable=new B(this),this._controllable=new L(this),h(this,"role","group"),this._controllable.initial(i=>i&&this._selectable.setState(i)),this._controllable.getter(()=>this._selectable.getState());let e=P();this._controllable.setter(e(i=>{this._selectable.setState(i)})),this._selectable.onChange(e(()=>{this._controllable.dispatch()})),b(this,"lofi-close-popovers",i=>{this.hasAttribute("keep-open")&&(i.preventDefault(),i.stopPropagation())})}},Ei=class extends S{boot(){this._selectable=new B(this,{multiple:!0}),this._controllable=new L(this),h(this,"role","group"),this._controllable.initial(i=>i&&this._selectable.setState(i)),this._controllable.getter(()=>this._selectable.getState());let e=P();this._controllable.setter(e(i=>{this._selectable.setState(i)})),this._selectable.onChange(e(()=>{this._controllable.dispatch()}))}};C(({css:t})=>t`ui-menu[popover]:popover-open { display: block; }`);C(({css:t})=>t`ui-menu[popover].\:popover-open { display: block; }`);C(({css:t})=>t`ui-menu-checkbox, ui-menu-radio { cursor: default; display: contents; }`);_("menu",Ai);_("submenu",xi);_("menu-checkbox",_i);_("menu-radio",Si);_("menu-radio-group",ki);_("menu-checkbox-group",Ei);function Ci(t){b(t,"keydown",e=>{e.key==="Enter"&&(t.click(),e.preventDefault(),e.stopPropagation())}),b(t,"keydown",e=>{e.key===" "&&(e.preventDefault(),e.stopPropagation())}),b(t,"keyup",e=>{e.key===" "&&(t.click(),e.preventDefault(),e.stopPropagation())})}function Go(t){t._disableable=new N(t),t._disabled=t.hasAttribute("disabled");let e=t.querySelector("a"),i=t,s=t.parentElement.matches("ui-submenu")&&t.parentElement.querySelector("ui-menu[popover]"),o=e||i;if(t._disabled&&(h(o,"disabled",""),h(o,"aria-disabled","true")),F(o,"menu-item"),h(o,"role","menuitem"),!t._disabled)if(o._focusable=new M(o,{disableable:t._disableable,hover:!0,tabbableAttr:"data-active"}),!s)t.hasAttribute("disabled")||b(t,"click",()=>{t.hasAttribute("keep-open")||t.dispatchEvent(new CustomEvent("lofi-close-popovers",{bubbles:!0}))}),Ci(i);else{s._popoverable=new V(s,{triggers:[i]}),s._anchorable=new z(s,{reference:i,position:s.hasAttribute("position")?s.getAttribute("position"):ze()?"left start":"right start",gap:s.hasAttribute("gap")?s.getAttribute("gap"):"-5",crossAxis:!0}),i.addEventListener("click",r=>{s._popoverable.setState(!0)});let{clear:n}=Te(i,s,{gain(){s._popoverable.setState(!0)},lose(){s._popoverable.setState(!1)},focusable:!1,useSafeArea:!0});s._popoverable.onChange(()=>{s._popoverable.getState()||(n(),s._focusable.wipeTabbables()),s._popoverable.getState()?s._anchorable.reposition():s._anchorable.cleanup()}),b(i,"keydown",r=>{r.key==="Enter"&&(s._popoverable.setState(!0),setTimeout(()=>s._focusable.focusFirst()))}),b(i,"keydown",r=>{r.key==="ArrowRight"&&(s._popoverable.setState(!0),setTimeout(()=>s._focusable.focusFirst()))}),b(s,"keydown",r=>{r.key==="ArrowLeft"&&(s._popoverable.setState(!1),i.focus(),r.stopPropagation())})}}document.addEventListener("alpine:init",()=>{let t=window.Flux?.applyAppearance;t||(t=()=>{window.Flux.appearance=null,window.localStorage.removeItem("flux.appearance")});let e=Alpine.reactive({toast(...s){let o={slots:{},dataset:{}};typeof s[0]=="string"&&(o.slots.text=s.shift()),typeof s[0]=="string"&&(o.slots.heading=o.slots.text,o.slots.text=s.shift());let n=s.shift()||{};n.text&&(o.slots.text=n.text),n.heading&&(o.slots.heading=n.heading),n.variant&&(o.dataset.variant=n.variant),n.position&&(o.dataset.position=n.position),n.duration!==void 0&&(o.duration=n.duration),document.dispatchEvent(new CustomEvent("toast-show",{detail:o}))},modal(s){return{show(){document.dispatchEvent(new CustomEvent("modal-show",{detail:{name:s}}))},close(){document.dispatchEvent(new CustomEvent("modal-close",{detail:{name:s}}))}}},modals(){return{close(){document.dispatchEvent(new CustomEvent("modal-close",{detail:{}}))}}},appearance:window.localStorage.getItem("flux.appearance")||"system",systemAppearanceChanged:1,get dark(){return JSON.stringify(e.systemAppearanceChanged),e.appearance==="system"?window.matchMedia("(prefers-color-scheme: dark)").matches:e.appearance==="dark"},set dark(s){let o=this.dark;s!==o&&(s?e.appearance="dark":e.appearance="light")}});window.Flux=e,Alpine.magic("flux",()=>e),Alpine.effect(()=>{t(e.appearance)}),document.addEventListener("livewire:navigated",()=>{t(e.appearance)}),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",()=>{e.systemAppearanceChanged++,t(e.appearance)})});!ji()&&!Ui()&&Yi();})();
