# 錄音室租借管理系統資料庫設計文件

## 一、系統概述

本系統旨在提供一個全面的錄音室租借管理平台，實現以下核心目標：
- 自動化預約：24小時線上查看空檔、自行預約並支付
- 提升管理效率：後台輕鬆管理錄音室、訂單與會員資料
- 優化顧客體驗：流暢預約流程、透明價格資訊與會員專屬優惠
- 數據化營運：透過報表分析了解營運狀況，制定精準行銷策略

## 二、使用者角色

### 顧客/會員
- 瀏覽錄音室資訊
- 註冊/登入會員
- 查看可預約時段
- 線上預約並支付
- 管理個人預約訂單與資料

### 管理員
- 管理錄音室資訊（新增、修改、刪除）
- 設定價格與預約規則
- 管理所有預約訂單
- 管理會員資料
- 查看營運報表

## 三、核心功能模組

1. **錄音室管理**：展示錄音室列表、詳情、設備、價格設定與狀態管理
2. **會員管理**：註冊/登入、會員資料、會員等級、預約記錄查看
3. **預約管理**：日曆視圖、線上預約流程、預約規則設定、訂單管理、自動通知
4. **電子支付**：金流串接、付款流程、付款狀態更新、退款處理
5. **門禁系統**：API串接、QR碼生成、掃碼進入錄音室

## 四、資料庫設計

### 1. users (Laravel預設使用者表)
儲存系統使用者的基本資訊
```
- id (主鍵)
- name (使用者名稱)
- email (電子郵件)
- password (密碼)
- remember_token
- email_verified_at
- created_at
- updated_at
```

### 2. user_profiles (使用者詳細資料)
擴展使用者資訊，儲存額外的會員資料
```
- id (主鍵)
- user_id (外鍵關聯users表)
- phone (電話)
- address (地址)
- member_level (會員等級: regular, vip等)
- points (積分)
- created_at
- updated_at
```

### 3. studios (錄音室)
儲存錄音室的基本資訊
```
- id (主鍵)
- name (錄音室名稱)
- description (詳細介紹)
- capacity (容納人數)
- size (坪數)
- location (位置)
- status (狀態: available, maintenance, closed)
- created_at
- updated_at
```

### 4. studio_images (錄音室圖片)
儲存錄音室的多張圖片
```
- id (主鍵)
- studio_id (外鍵關聯studios表)
- image_path (圖片路徑)
- is_cover (是否為封面圖)
- created_at
- updated_at
```

### 5. equipments (設備)
儲存可用設備的資訊
```
- id (主鍵)
- name (設備名稱)
- description (設備描述)
- type (設備類型: microphone, speaker等)
- created_at
- updated_at
```

### 6. studio_equipments (錄音室設備關聯)
多對多關聯表，連接錄音室和設備
```
- id (主鍵)
- studio_id (外鍵關聯studios表)
- equipment_id (外鍵關聯equipments表)
- quantity (數量)
- created_at
- updated_at
```

### 7. pricing_plans (價格方案)
儲存不同錄音室的價格方案
```
- id (主鍵)
- studio_id (外鍵關聯studios表)
- name (方案名稱)
- type (類型: weekday, weekend, peak, off-peak)
- hourly_rate (每小時價格)
- min_hours (最小預約時長)
- discount_percentage (折扣百分比)
- discount_hours (達到折扣所需小時數)
- created_at
- updated_at
```

### 8. bookings (預約)
儲存使用者的預約資訊
```
- id (主鍵)
- user_id (外鍵關聯users表)
- studio_id (外鍵關聯studios表)
- start_time (開始時間)
- end_time (結束時間)
- total_hours (總小時數)
- total_amount (總金額)
- status (狀態: pending, confirmed, cancelled, completed)
- notes (備註)
- created_at
- updated_at
```

### 9. payments (支付)
儲存預約相關的支付資訊
```
- id (主鍵)
- booking_id (外鍵關聯bookings表)
- amount (金額)
- payment_method (支付方式: credit_card, atm等)
- transaction_id (交易ID)
- status (狀態: pending, completed, refunded)
- created_at
- updated_at
```

### 10. access_codes (門禁碼)
儲存預約的門禁QR碼資訊
```
- id (主鍵)
- booking_id (外鍵關聯bookings表)
- qr_code (QR碼內容)
- valid_from (有效開始時間)
- valid_until (有效結束時間)
- is_used (是否已使用)
- created_at
- updated_at
```

### 11. notifications (通知)
儲存系統發送給使用者的通知
```
- id (主鍵)
- user_id (外鍵關聯users表)
- booking_id (外鍵關聯bookings表)
- type (類型: booking_confirmation, reminder, cancellation)
- content (內容)
- is_read (是否已讀)
- sent_at (發送時間)
- created_at
- updated_at
```

### 12. booking_rules (預約規則)
儲存系統預約規則的配置
```
- id (主鍵)
- min_booking_hours (最小預約時長)
- buffer_time_minutes (預約間隔緩衝時間)
- advance_booking_days (可提前預約天數)
- cancellation_policy (取消政策)
- created_at
- updated_at
```

## 五、表关系说明

1. **用户与用户资料**：一对一关系
   - 一个用户对应一份详细资料

2. **录音室与图片**：一对多关系
   - 一个录音室可以有多张图片

3. **录音室与设备**：多对多关系
   - 一个录音室可以有多种设备
   - 一种设备可以在多个录音室中使用
   - 通过studio_equipments表建立关联

4. **录音室与价格方案**：一对多关系
   - 一个录音室可以有多种价格方案（平日、假日、尖峰等）

5. **用户与预约**：一对多关系
   - 一个用户可以有多个预约记录

6. **录音室与预约**：一对多关系
   - 一个录音室可以有多个预约记录

7. **预约与支付**：一对一关系
   - 一个预约对应一笔支付记录

8. **预约与门禁码**：一对一关系
   - 一个预约对应一个门禁QR码

9. **用户与通知**：一对多关系
   - 一个用户可以收到多条通知

## 六、用户预约流程

1. **浏览与选择**：用户进入网站，浏览不同录音室
2. **查看档期**：点击感兴趣的录音室，查看可预约时段
3. **选择时段**：在日历上选择想要的日期和时间
4. **确认信息**：系统显示预约摘要，包括录音室、时段、总金额
5. **登入/注册**：未登入用户需完成登入或注册
6. **支付费用**：跳转至第三方金流页面完成支付
7. **完成预约**：显示预约成功信息，发送确认邮件和门禁QR码
8. **使用录音室**：预约时间到时，扫描QR码进入录音室

## 七、系统扩展考虑

1. **评价系统**：允许用户对使用过的录音室进行评价
2. **积分系统**：用户预约可累积积分，兑换优惠
3. **优惠券管理**：发放和使用优惠券
4. **设备租借**：额外设备的租借管理
5. **多语言支持**：支持多种语言界面
6. **数据分析**：更详细的使用数据分析和报表