# 錄音室租借管理系統資料庫設計文件

## 一、系統概述

本系統旨在提供一個全面的錄音室租借管理平台，實現以下核心目標：
- 自動化預約：24小時線上查看空檔、自行預約並支付
- 提升管理效率：後台輕鬆管理錄音室、訂單與會員資料
- 優化顧客體驗：流暢預約流程、透明價格資訊與會員專屬優惠
- 數據化營運：透過報表分析了解營運狀況，制定精準行銷策略

## 二、使用者角色

### 顧客/會員
- 瀏覽錄音室資訊
- 註冊/登入會員
- 查看可預約時段
- 線上預約並支付
- 管理個人預約訂單與資料

### 管理員
- 管理錄音室資訊（新增、修改、刪除）
- 設定價格與預約規則
- 管理所有預約訂單
- 管理會員資料
- 查看營運報表

## 三、核心功能模組

1. **錄音室管理**：展示錄音室列表、詳情、設備、價格設定與狀態管理
2. **會員管理**：註冊/登入、會員資料、會員等級、預約記錄查看
3. **預約管理**：日曆視圖、線上預約流程、預約規則設定、訂單管理、自動通知
4. **電子支付**：金流串接、付款流程、付款狀態更新、退款處理
5. **門禁系統**：API串接、QR碼生成、掃碼進入錄音室

## 四、資料庫設計

### 1. users (Laravel預設使用者表)
儲存系統使用者的基本資訊
```
- id (主鍵)
- name (使用者名稱)
- email (電子郵件)
- password (密碼)
- remember_token
- email_verified_at
- created_at
- updated_at
```

### 2. user_profiles (使用者詳細資料)
擴展使用者資訊，儲存額外的會員資料
```
- id (主鍵)
- user_id (外鍵關聯users表)
- phone (電話)
- address (地址)
- member_level (會員等級: regular, vip等)
- points (積分)
- created_at
- updated_at
```

### 3. studios (錄音室)
儲存錄音室的基本資訊
```
- id (主鍵)
- name (錄音室名稱)
- description (詳細介紹)
- capacity (容納人數)
- size (坪數)
- location (位置)
- status (狀態: available, maintenance, closed)
- created_at
- updated_at
```

### 4. studio_images (錄音室圖片)
儲存錄音室的多張圖片
```
- id (主鍵)
- studio_id (外鍵關聯studios表)
- image_path (圖片路徑)
- is_cover (是否為封面圖)
- created_at
- updated_at
```

### 5. equipments (設備)
儲存可用設備的資訊
```
- id (主鍵)
- name (設備名稱)
- description (設備描述)
- type (設備類型: microphone, speaker等)
- created_at
- updated_at
```

### 6. studio_equipments (錄音室設備關聯)
多對多關聯表，連接錄音室和設備
```
- id (主鍵)
- studio_id (外鍵關聯studios表)
- equipment_id (外鍵關聯equipments表)
- quantity (數量)
- created_at
- updated_at
```

### 7. pricing_plans (價格方案)
儲存不同錄音室的價格方案
```
- id (主鍵)
- studio_id (外鍵關聯studios表)
- name (方案名稱)
- type (類型: weekday, weekend, peak, off-peak)
- hourly_rate (每小時價格)
- min_hours (最小預約時長)
- discount_percentage (折扣百分比)
- discount_hours (達到折扣所需小時數)
- created_at
- updated_at
```

### 8. bookings (預約)
儲存使用者的預約資訊
```
- id (主鍵)
- user_id (外鍵關聯users表)
- studio_id (外鍵關聯studios表)
- start_time (開始時間)
- end_time (結束時間)
- total_hours (總小時數)
- total_amount (總金額)
- status (狀態: pending, confirmed, cancelled, completed)
- notes (備註)
- created_at
- updated_at
```

### 9. payments (支付)
儲存預約相關的支付資訊
```
- id (主鍵)
- booking_id (外鍵關聯bookings表)
- amount (金額)
- payment_method (支付方式: credit_card, atm等)
- transaction_id (交易ID)
- status (狀態: pending, completed, refunded)
- created_at
- updated_at
```

### 10. access_codes (門禁碼)
儲存預約的門禁QR碼資訊
```
- id (主鍵)
- booking_id (外鍵關聯bookings表)
- qr_code (QR碼內容)
- valid_from (有效開始時間)
- valid_until (有效結束時間)
- is_used (是否已使用)
- created_at
- updated_at
```

### 11. notifications (通知)
儲存系統發送給使用者的通知
```
- id (主鍵)
- user_id (外鍵關聯users表)
- booking_id (外鍵關聯bookings表)
- type (類型: booking_confirmation, reminder, cancellation)
- content (內容)
- is_read (是否已讀)
- sent_at (發送時間)
- created_at
- updated_at
```

### 12. booking_rules (預約規則)
儲存系統預約規則的配置
```
- id (主鍵)
- min_booking_hours (最小預約時長)
- buffer_time_minutes (預約間隔緩衝時間)
- advance_booking_days (可提前預約天數)
- cancellation_policy (取消政策)
- created_at
- updated_at
```

## 五、表關係說明

1. **使用者與使用者資料**：一對一關係
   - 一個使用者對應一份詳細資料

2. **錄音室與圖片**：一對多關係
   - 一個錄音室可以有多張圖片

3. **錄音室與設備**：多對多關係
   - 一個錄音室可以有多種設備
   - 一種設備可以在多個錄音室中使用
   - 透過studio_equipments表建立關聯

4. **錄音室與價格方案**：一對多關係
   - 一個錄音室可以有多種價格方案（平日、假日、尖峰等）

5. **使用者與預約**：一對多關係
   - 一個使用者可以有多個預約記錄

6. **錄音室與預約**：一對多關係
   - 一個錄音室可以有多個預約記錄

7. **預約與支付**：一對一關係
   - 一個預約對應一筆支付記錄

8. **預約與門禁碼**：一對一關係
   - 一個預約對應一個門禁QR碼

9. **使用者與通知**：一對多關係
   - 一個使用者可以收到多條通知

## 六、使用者預約流程

1. **瀏覽與選擇**：使用者進入網站，瀏覽不同錄音室
2. **查看檔期**：點擊感興趣的錄音室，查看可預約時段
3. **選擇時段**：在日曆上選擇想要的日期和時間
4. **確認資訊**：系統顯示預約摘要，包括錄音室、時段、總金額
5. **登入/註冊**：未登入使用者需完成登入或註冊
6. **支付費用**：跳轉至第三方金流頁面完成支付
7. **完成預約**：顯示預約成功資訊，發送確認郵件和門禁QR碼
8. **使用錄音室**：預約時間到時，掃描QR碼進入錄音室

## 七、系統擴展考慮

1. **評價系統**：允許使用者對使用過的錄音室進行評價
2. **積分系統**：使用者預約可累積積分，兌換優惠
3. **優惠券管理**：發放和使用優惠券
4. **設備租借**：額外設備的租借管理
5. **多語言支援**：支援多種語言介面
6. **數據分析**：更詳細的使用數據分析和報表