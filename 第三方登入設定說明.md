# 第三方登入設定說明

## 環境變數設定

請在您的 `.env` 檔案中添加以下設定：

```env
# Google OAuth 設定
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# LINE Login 設定
LINE_CLIENT_ID=your_line_channel_id
LINE_CLIENT_SECRET=your_line_channel_secret

# Facebook Login 設定（可選）
FACEBOOK_CLIENT_ID=your_facebook_app_id
FACEBOOK_CLIENT_SECRET=your_facebook_app_secret
```

## Google OAuth 設定步驟

1. 前往 [Google Cloud Console](https://console.cloud.google.com/)
2. 建立新專案或選擇現有專案
3. 啟用 Google+ API
4. 建立 OAuth 2.0 憑證
5. 設定授權重新導向 URI：
   - `http://localhost:8000/auth/google/callback` (開發環境)
   - `https://yourdomain.com/auth/google/callback` (正式環境)

## LINE Login 設定步驟

1. 前往 [LINE Developers Console](https://developers.line.biz/)
2. 建立新的 Provider 和 Channel
3. 選擇 "LINE Login" 類型
4. 設定 Callback URL：
   - `http://localhost:8000/auth/line/callback` (開發環境)
   - `https://yourdomain.com/auth/line/callback` (正式環境)
5. 取得 Channel ID 和 Channel Secret

## Facebook Login 設定步驟

1. 前往 [Facebook for Developers](https://developers.facebook.com/)
2. 建立新應用程式
3. 添加 Facebook Login 產品
4. 設定有效的 OAuth 重新導向 URI：
   - `http://localhost:8000/auth/facebook/callback` (開發環境)
   - `https://yourdomain.com/auth/facebook/callback` (正式環境)

## 測試第三方登入

設定完成後，您可以：

1. 啟動開發伺服器：`php artisan serve`
2. 前往註冊頁面：`http://localhost:8000/register`
3. 點擊 Google 或 LINE 登入按鈕進行測試

## 注意事項

- 確保您的應用程式 URL 設定正確
- 在正式環境中使用 HTTPS
- 妥善保管您的 Client Secret
- 定期檢查和更新 OAuth 應用程式設定

## 支援的登入方式

目前系統支援：
- ✅ 電子郵件註冊/登入
- ✅ Google OAuth
- ✅ LINE Login
- 🔄 Facebook Login（已準備，需設定）
- 🔄 Apple Sign In（可擴展）

## 資料庫結構

系統會自動：
1. 建立使用者帳號（如果不存在）
2. 在 `social_accounts` 表中記錄第三方登入資訊
3. 支援一個使用者綁定多個第三方帳號
4. 自動同步頭像和基本資料
