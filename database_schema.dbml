// 錄音室租借管理系統資料庫設計
// 使用方法：複製此內容到 dbdiagram.io

Table users {
  id bigint [pk, increment]
  name varchar
  email varchar [unique]
  password varchar
  email_verified_at timestamp
  remember_token varchar
  created_at timestamp
  updated_at timestamp
}

Table user_profiles {
  id bigint [pk, increment]
  user_id bigint [ref: - users.id]
  phone varchar
  address text
  member_level varchar [default: 'regular']
  points integer [default: 0]
  created_at timestamp
  updated_at timestamp
}

Table studios {
  id bigint [pk, increment]
  name varchar
  description text
  capacity integer [default: 1]
  size decimal
  location varchar
  status varchar [default: 'available']
  created_at timestamp
  updated_at timestamp
}

Table studio_images {
  id bigint [pk, increment]
  studio_id bigint [ref: > studios.id]
  image_path varchar
  is_cover boolean [default: false]
  created_at timestamp
  updated_at timestamp
}

Table equipments {
  id bigint [pk, increment]
  name varchar
  description text
  type varchar [default: 'other']
  created_at timestamp
  updated_at timestamp
}

Table studio_equipments {
  id bigint [pk, increment]
  studio_id bigint [ref: > studios.id]
  equipment_id bigint [ref: > equipments.id]
  quantity integer [default: 1]
  created_at timestamp
  updated_at timestamp
}

Table pricing_plans {
  id bigint [pk, increment]
  studio_id bigint [ref: > studios.id]
  name varchar
  type varchar [default: 'weekday']
  hourly_rate decimal
  min_hours integer [default: 1]
  discount_percentage decimal
  discount_hours integer
  created_at timestamp
  updated_at timestamp
}

Table bookings {
  id bigint [pk, increment]
  user_id bigint [ref: > users.id]
  studio_id bigint [ref: > studios.id]
  start_time datetime
  end_time datetime
  total_hours decimal
  total_amount decimal
  status varchar [default: 'pending']
  notes text
  created_at timestamp
  updated_at timestamp
}

Table payments {
  id bigint [pk, increment]
  booking_id bigint [ref: - bookings.id]
  amount decimal
  payment_method varchar [default: 'credit_card']
  transaction_id varchar
  status varchar [default: 'pending']
  payment_details json
  created_at timestamp
  updated_at timestamp
}

Table access_codes {
  id bigint [pk, increment]
  booking_id bigint [ref: - bookings.id]
  qr_code text
  valid_from datetime
  valid_until datetime
  is_used boolean [default: false]
  used_at datetime
  created_at timestamp
  updated_at timestamp
}

Table notifications {
  id bigint [pk, increment]
  user_id bigint [ref: > users.id]
  booking_id bigint [ref: > bookings.id]
  type varchar [default: 'other']
  title varchar
  content text
  is_read boolean [default: false]
  sent_at datetime
  created_at timestamp
  updated_at timestamp
}

Table booking_rules {
  id bigint [pk, increment]
  min_booking_hours integer [default: 1]
  buffer_time_minutes integer [default: 15]
  advance_booking_days integer [default: 30]
  cancellation_policy text
  max_booking_hours_per_day integer [default: 8]
  max_concurrent_bookings integer [default: 3]
  allow_weekend_booking boolean [default: true]
  booking_start_time time [default: '08:00:00']
  booking_end_time time [default: '22:00:00']
  created_at timestamp
  updated_at timestamp
}
